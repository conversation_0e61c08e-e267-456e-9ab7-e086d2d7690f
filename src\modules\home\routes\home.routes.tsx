import type { RouteObject } from "react-router";
import { HomeContainer } from "../containers/home.container";
import { HomeLayoutContainer } from "../containers/home-layout.container";
import { RouteErrorBoundary } from "@/shared/components/error-boundary";

export const HOME_ROUTES = {
  ROOT: "",
} as const;

export const homeRoutes: RouteObject[] = [
  {
    path: HOME_ROUTES.ROOT,
    element: <HomeLayoutContainer />,
    errorElement: <RouteErrorBoundary />,
    children: [
      {
        index: true,
        element: <HomeContainer />,
        errorElement: <RouteErrorBoundary />,
      },
    ],
  },
];
