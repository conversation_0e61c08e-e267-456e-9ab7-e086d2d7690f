import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { endOfDay, format } from "date-fns";

export interface FilterState {
  appliedCompany: string | null;
  appliedVehicle: string | null;
  appliedDateRange: {
    selectedDate: Date | null;
    timeFrom: string | null;
    timeTo: string | null;
  };

  pendingCompany: string | null;
  pendingVehicle: string | null;
  pendingDateRange: {
    selectedDate: Date | null;
    timeFrom: string | null;
    timeTo: string | null;
  };

  setPendingCompany: (company: string | null) => void;
  setPendingVehicle: (vehicle: string | null) => void;
  setPendingDateRange: (range: {
    selectedDate?: Date | null;
    timeFrom?: string | null;
    timeTo?: string | null;
  }) => void;
  applyPendingFilters: () => void;
  initializePendingWithApplied: () => void;
  clearPendingFilters: () => void;
  clearPendingToDefaults: () => void;
  hasPendingChanges: () => boolean;

  clearAllFilters: () => void;
  getActiveFiltersCount: () => number;
  hasActiveFilters: () => boolean;
}

const initialState = {
  appliedCompany: null,
  appliedVehicle: null,
  appliedDateRange: {
    selectedDate: new Date(),
    timeFrom: "00:00",
    timeTo: format(endOfDay(new Date()), "HH:mm"),
  },
  pendingCompany: null,
  pendingVehicle: null,
  pendingDateRange: {
    selectedDate: new Date(),
    timeFrom: "00:00",
    timeTo: format(endOfDay(new Date()), "HH:mm"),
  },
};

export const useFilterStore = create<FilterState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      setPendingCompany: (company) =>
        set({ pendingCompany: company }, false, "setPendingCompany"),
      setPendingVehicle: (vehicle) =>
        set({ pendingVehicle: vehicle }, false, "setPendingVehicle"),
      setPendingDateRange: (range) =>
        set(
          (state) => ({
            pendingDateRange: { ...state.pendingDateRange, ...range },
          }),
          false,
          "setPendingDateRange"
        ),

      applyPendingFilters: () => {
        const state = get();
        set(
          {
            appliedCompany: state.pendingCompany,
            appliedVehicle: state.pendingVehicle,
            appliedDateRange: state.pendingDateRange,
          },
          false,
          "applyPendingFilters"
        );
      },

      initializePendingWithApplied: () => {
        const state = get();
        set(
          {
            pendingCompany: state.appliedCompany,
            pendingVehicle: state.appliedVehicle,
            pendingDateRange: state.appliedDateRange,
          },
          false,
          "initializePendingWithApplied"
        );
      },

      clearPendingFilters: () => {
        const state = get();
        set(
          {
            pendingCompany: state.appliedCompany,
            pendingVehicle: state.appliedVehicle,
            pendingDateRange: state.appliedDateRange,
          },
          false,
          "clearPendingFilters"
        );
      },

      clearPendingToDefaults: () => {
        set(
          {
            pendingCompany: null,
            pendingVehicle: null,
            pendingDateRange: initialState.pendingDateRange,
          },
          false,
          "clearPendingToDefaults"
        );
      },

      hasPendingChanges: () => {
        const state = get();
        return (
          state.pendingCompany !== state.appliedCompany ||
          state.pendingVehicle !== state.appliedVehicle ||
          JSON.stringify(state.pendingDateRange) !==
            JSON.stringify(state.appliedDateRange)
        );
      },

      clearAllFilters: () =>
        set(
          {
            appliedCompany: null,
            appliedVehicle: null,
            appliedDateRange: initialState.appliedDateRange,
            pendingCompany: null,
            pendingVehicle: null,
            pendingDateRange: initialState.pendingDateRange,
          },
          false,
          "clearAllFilters"
        ),

      getActiveFiltersCount: () => {
        const state = get();
        let count = 0;
        if (state.appliedCompany) count++;
        if (state.appliedVehicle) count++;
        if (state.appliedDateRange.selectedDate) count++;
        return count;
      },

      hasActiveFilters: () => {
        return get().getActiveFiltersCount() > 0;
      },
    }),
    {
      name: "filter-store",
    }
  )
);

export const useAppliedCompany = () =>
  useFilterStore((state) => state.appliedCompany);
export const usePendingCompany = () =>
  useFilterStore((state) => state.pendingCompany);
export const useAppliedVehicle = () =>
  useFilterStore((state) => state.appliedVehicle);
export const usePendingVehicle = () =>
  useFilterStore((state) => state.pendingVehicle);
export const useAppliedDateRange = () =>
  useFilterStore((state) => state.appliedDateRange);
export const usePendingDateRange = () =>
  useFilterStore((state) => state.pendingDateRange);
export const useStaticHasPendingChanges = () =>
  useFilterStore((state) => state.hasPendingChanges());
export const useActiveFiltersCount = () =>
  useFilterStore((state) => state.getActiveFiltersCount());
