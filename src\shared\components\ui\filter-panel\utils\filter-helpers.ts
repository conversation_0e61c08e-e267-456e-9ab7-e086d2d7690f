import { ReactElement } from "react";
import type { FilterValue } from "@components/filter-panel/stores/dynamic-filter-store";

function isDependencySatisfied(
  dependency: string,
  pendingCompany: string | null,
  appliedFilters: Record<string, FilterValue>
): boolean {
  if (dependency === "company") {
    return !!pendingCompany;
  }

  return !!(
    appliedFilters[dependency] &&
    (Array.isArray(appliedFilters[dependency])
      ? (appliedFilters[dependency] as string[]).length > 0
      : appliedFilters[dependency] !== "")
  );
}

export function isFilterDisabled(
  componentName: string,
  child: ReactElement & { props: { dependsOn?: string | string[] } },
  pendingCompany: string | null,
  appliedFilters: Record<string, FilterValue> = {}
): boolean {
  if (componentName === "PlanFilter") return !pendingCompany;
  if (componentName === "VehicleFilter") return !pendingCompany;
  if (componentName === "VideoStatusFilter") return true;
  // ! We should add more core dependencies like plan, vehicle, etc. if they depend on other filters HERE

  // Check for custom dependencies in child props
  if (child.props?.dependsOn) {
    const dependencies = Array.isArray(child.props.dependsOn)
      ? child.props.dependsOn
      : [child.props.dependsOn];

    for (const dependency of dependencies) {
      if (!isDependencySatisfied(dependency, pendingCompany, appliedFilters)) {
        return true;
      }
    }
  }

  return false;
}

export function isFilterLoading(
  componentName: string,
  child: ReactElement & {
    props: { dependsOn?: string | string[] } & {
      planLoading?: boolean;
      companiesLoading?: boolean;
      loading?: boolean;
    };
  },
  pendingCompany: string | null,
  appliedCompany: string | null,
  appliedFilters: Record<string, FilterValue> = {}
): boolean {
  const isDisabled = isFilterDisabled(
    componentName,
    child,
    pendingCompany,
    appliedFilters
  );
  if (isDisabled) return false;

  if (componentName === "PlanFilter")
    return !!appliedCompany && !!child.props?.planLoading;
  // ! We should add more core dependencies like vehicle, etc. if they depend on other filters HERE

  if (child.props?.dependsOn) {
    const dependencies = Array.isArray(child.props.dependsOn)
      ? child.props.dependsOn
      : [child.props.dependsOn];

    if (
      dependencies.includes("company") &&
      !!appliedCompany &&
      !!child.props?.loading
    ) {
      return true;
    }
  }

  return !!(
    child.props?.loading ||
    child.props?.companiesLoading ||
    child.props?.planLoading
  );
}

export function getDisabledReason(
  componentName: string,
  child: ReactElement & { props: { dependsOn?: string | string[] } }
): string {
  if (componentName === "PlanFilter") return "Selecciona un distrito primero";
  if (componentName === "VehicleFilter")
    return "Selecciona un distrito primero";
  if (componentName === "VideoStatusFilter") return "Pendiente desarrollo";
  // ! We should add more core dependencies like vehicle, etc. if they depend on other filters HERE

  if (child.props?.dependsOn) {
    const dependencies = Array.isArray(child.props.dependsOn)
      ? child.props.dependsOn
      : [child.props.dependsOn];

    if (dependencies.includes("company")) {
      return "Selecciona un distrito primero";
    }

    const firstDependency = dependencies[0];
    return `Selecciona ${firstDependency} primero`;
  }

  return "Filtro no disponible";
}
