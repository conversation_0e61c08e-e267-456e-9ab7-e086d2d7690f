import type { ErrorInfo, ReactNode } from "react";
import type { FallbackProps } from "react-error-boundary";

import { Suspense } from "react";
import { ErrorBoundary } from "react-error-boundary";

import { DefaultErrorFallback } from "./default-error-fallback";
import { handleError } from "./utils/handle-error";

const LoadingSpinner = () => {
  return <div>Loading...</div>;
};

export const ComponentErrorBoundary: React.FC<{
  children: ReactNode;
  fallback?: React.ComponentType<FallbackProps>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  onReset?: () => void;
  resetKeys?: Array<string | number>;
  resetOnPropsChange?: boolean;
  isolate?: boolean;
}> = ({
  children,
  fallback: FallbackComponent = DefaultErrorFallback,
  onError,
  onReset,
  resetKeys,
  resetOnPropsChange = true,
  isolate = false,
}) => {
  const handleReset = () => {
    onReset?.();
  };

  const handleErrorBoundary = (error: Error, errorInfo: ErrorInfo) => {
    if (onError) {
      onError(error, errorInfo);
    } else {
      handleError(error, errorInfo.componentStack || "Error Boundary");
    }
  };

  const errorBoundaryProps = {
    FallbackComponent,
    onError: handleErrorBoundary,
    onReset: handleReset,
    ...(resetKeys && { resetKeys }),
    ...(resetOnPropsChange && { resetOnPropsChange }),
  };

  const content = isolate ? (
    <div style={{ isolation: "isolate" }}>
      <Suspense fallback={<LoadingSpinner />}>{children}</Suspense>
    </div>
  ) : (
    <Suspense fallback={<LoadingSpinner />}>{children}</Suspense>
  );

  return <ErrorBoundary {...errorBoundaryProps}>{content}</ErrorBoundary>;
};
