import { cva } from "class-variance-authority";

export const radioGroupItemStyles = `
  aspect-square
  h-4
  w-4
  rounded-full
  border-[1.8px]
  border-primary-800
  text-primary
  ring-offset-background
  focus:outline-none
  focus-visible:ring-2
  focus-visible:ring-ring
  focus-visible:ring-offset-2
  disabled:cursor-not-allowed
  disabled:border-gray-600
`;

export const radioGroupContainerStyles = cva(
  "flex items-center gap-2",
  {
    variants: {
      variant: {
        default: "",
        hover: "p-2.5 hover:bg-gray-200",
      },
      labelPosition: {
        left: "flex-row-reverse",
        right: "flex-row",
      },
      defaultVariants: {
        hover: false,
        labelPosition: "right",
      },
    },
  },
);

export const radioGroupIndicatorStyles = "flex items-center justify-center";

export const radioGroupCircleStyles = cva(
  "h-[9px] w-[9px]",
  {
    variants: {
      state: {
        default: "fill-primary-800 stroke-primary-800",
        disabled: "fill-gray-600 stroke-gray-600",
      },
    },
    defaultVariants: {
      state: "default",
    },
  },
);

export const radioGroupLabelStyles = cva(
  "cursor-pointer text-sm font-medium leading-none",
  {
    variants: {
      state: {
        default: "text-gray-1000",
        disabled: "cursor-not-allowed text-gray-600",
      },
    },
    defaultVariants: {
      state: "default",
    },
  },
);
