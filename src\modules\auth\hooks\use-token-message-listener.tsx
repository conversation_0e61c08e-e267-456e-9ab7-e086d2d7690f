import { useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";

export const useTokenMessageListener = () => {
  const queryClient = useQueryClient();

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.authToken) {
        const newToken = event.data.authToken;
        queryClient.setQueryData(["authToken"], newToken);
      }
    };

    window.addEventListener("message", handleMessage);
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, [queryClient]);
};
