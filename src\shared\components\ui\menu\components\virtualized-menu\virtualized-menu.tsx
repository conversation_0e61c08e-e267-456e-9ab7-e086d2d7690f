import { useVirtualizer } from "@tanstack/react-virtual";
import { useRef, useState, useMemo } from "react";
import { ListItem } from "@components/list-item";
import { Input, SearchIcon, MenuItem } from "@urbetrack/urbix";

interface VirtualizedMenuProps {
  items: MenuItem[];
  selectedItems?: string[];
  onSelectedItemsChange?: (items: string[]) => void;
  searchable?: boolean;
  inputPlaceholder?: string;
  estimatedItemSize?: number;
  overscan?: number;
  containerHeight?: string;
  className?: string;
}

export const VirtualizedMenu = ({
  items,
  selectedItems = [],
  onSelectedItemsChange,
  searchable = false,
  inputPlaceholder = "Buscar",
  estimatedItemSize = 48,
  overscan = 5,
  containerHeight = "100%",
  className,
}: VirtualizedMenuProps) => {
  const parentRef = useRef<HTMLDivElement>(null);
  const [searchQuery, setSearchQuery] = useState("");

  // Filter items based on search query
  const filteredItems = useMemo(() => {
    if (!searchQuery.trim()) {
      return items;
    }
    return items.filter(item =>
      item.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [items, searchQuery]);

  const virtualizer = useVirtualizer({
    count: filteredItems.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => estimatedItemSize,
    overscan,
  });

  const handleItemToggle = (itemValue: string, checked: boolean) => {
    const newSelectedItems = checked
      ? [...selectedItems, itemValue]
      : selectedItems.filter(item => item !== itemValue);
    
    onSelectedItemsChange?.(newSelectedItems);
  };

  return (
    <div className={`h-full flex flex-col ${className || ""}`}>
      {searchable && (
        <div className="mb-3 flex-shrink-0">
          <Input
            placeholder={inputPlaceholder}
            iconRight={<SearchIcon />}
            value={searchQuery}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              setSearchQuery(e.target.value);
            }}
          />
        </div>
      )}
      
      {filteredItems.length === 0 ? (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          No se encontraron opciones
        </div>
      ) : (
        <div 
          ref={parentRef}
          className="flex-1 overflow-auto"
          style={{ height: searchable ? "calc(100% - 60px)" : containerHeight }}
        >
          <div
            style={{
              height: `${virtualizer.getTotalSize()}px`,
              width: '100%',
              position: 'relative',
            }}
          >
            {virtualizer.getVirtualItems().map((virtualItem) => {
              const item = filteredItems[virtualItem.index];
              const isSelected = selectedItems.includes(item.value);
              
              return (
                <div
                  key={virtualItem.key}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: `${virtualItem.size}px`,
                    transform: `translateY(${virtualItem.start}px)`,
                  }}
                >
                  <ListItem
                    checkbox
                    checkOnClick
                    checked={isSelected}
                    onChecked={(checked) => handleItemToggle(item.value, !!checked)}
                  >
                    {item.label}
                  </ListItem>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}; 