import { useState, useRef, useEffect } from "react";
import { MdSearch, MdClose } from "react-icons/md";
import { motion, AnimatePresence } from "framer-motion";
import { Input } from "@components/input";
import { cn } from "@styles/cn";
import { ButtonWithIcon } from "@components/button/button-with-icon";

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  debounceMs?: number;
}

export function SearchBar({
  value,
  onChange,
  placeholder = "Buscar...",
  className,
  debounceMs = 300,
}: SearchBarProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();
  const prevValueRef = useRef(value);

  if (prevValueRef.current !== value) {
    prevValueRef.current = value;
    setInputValue(value);
  }

  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      onChange(inputValue);
    }, debounceMs);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [inputValue, onChange, debounceMs]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isExpanded &&
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        !inputValue.trim()
      ) {
        setIsExpanded(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isExpanded, inputValue]);

  const handleSearchClick = () => {
    setIsExpanded(true);
    setTimeout(() => {
      inputRef.current?.focus();
    }, 150);
  };

  const handleBlur = () => {
    if (!inputValue.trim()) {
      setIsExpanded(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Escape") {
      setInputValue("");
      setIsExpanded(false);
      inputRef.current?.blur();
    }
  };

  const handleClear = () => {
    setInputValue("");
    inputRef.current?.focus();
  };

  return (
    <div ref={containerRef} className={cn("relative flex items-center", className)}>
      <AnimatePresence mode="wait">
        {!isExpanded ? (
          <motion.div
            key="search-button"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            <ButtonWithIcon
              variant="link"
              icon={<MdSearch className="h-5 w-5" />}
              onClick={handleSearchClick}
              aria-label="Abrir búsqueda"
            />
          </motion.div>
        ) : (
          <motion.div
            key="search-input"
            className="relative flex items-center"
            initial={{ width: 40, opacity: 0 }}
            animate={{ width: "25rem", opacity: 1 }}
            exit={{ width: 40, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            <Input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onBlur={handleBlur}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className="pr-10"
              aria-label="Buscar en la tabla"
            />
            {inputValue ? (
              <button
                type="button"
                onClick={handleClear}
                className="absolute right-3 h-5 w-5 flex items-center justify-center text-gray-400 hover:text-gray-600 focus:outline-none"
                aria-label="Limpiar búsqueda"
              >
                <MdClose className="h-5 w-5" />
              </button>
            ) : (
              <MdSearch className="absolute right-3 h-5 w-5 text-gray-400 pointer-events-none" />
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
