import type { CSSProperties } from "react";
import type { ToastContainerProps as ToastifyContainerProps } from "react-toastify";
import { ToastContainer as ToastifyContainer } from "react-toastify";

export type ToastPosition = {
  top?: string | number;
  right?: string | number;
  bottom?: string | number;
  left?: string | number;
  zIndex?: number;
};

export type ToastTransitionOptions = {
  property?: string;
  duration?: string;
  timingFunction?: string;
  delay?: string;
};

export type PositioningStrategy = () => ToastPosition;

export type ToastContainerProps = Omit<ToastifyContainerProps, "style" | "transition"> & {
  positioning?: PositioningStrategy;
  transition?: ToastTransitionOptions;
  staticPosition?: ToastPosition;
  style?: CSSProperties;
};

export function ToastContainer({
  positioning,
  transition,
  staticPosition,
  style,
  position = "top-right",
  autoClose = 3000,
  hideProgressBar = false,
  newestOnTop = false,
  closeOnClick = true,
  rtl = false,
  pauseOnFocusLoss = true,
  draggable = true,
  pauseOnHover = true,
  theme = "light",
  limit = 4,
  ...rest
}: ToastContainerProps) {
  const dynamicPosition = positioning?.();

  const finalPosition = dynamicPosition || staticPosition || {
    top: "1rem",
    right: "1rem",
    zIndex: 9999,
  };

  const transitionString = transition
    ? `${transition.property || "all"} ${transition.duration || "0.3s"} ${transition.timingFunction || "ease-in-out"} ${transition.delay || "0s"}`
    : undefined;

  const combinedStyle: CSSProperties = {
    ...finalPosition,
    ...(transitionString && { transition: transitionString }),
    ...style,
  };

  return (
    <ToastifyContainer
      position={position}
      autoClose={autoClose}
      hideProgressBar={hideProgressBar}
      newestOnTop={newestOnTop}
      closeOnClick={closeOnClick}
      rtl={rtl}
      pauseOnFocusLoss={pauseOnFocusLoss}
      draggable={draggable}
      pauseOnHover={pauseOnHover}
      theme={theme}
      limit={limit}
      style={combinedStyle}
      toastClassName={(context) => {
        const defaultClasses = context?.defaultClassName || "";
        const baseClasses = "relative before:content-[''] before:absolute before:left-0 before:top-0 before:bottom-0 before:w-1 before:rounded-l-md";
        const typeClasses = {
          success: "before:bg-green-500 bg-green-50",
          info: "before:bg-blue-500 bg-blue-50", 
          warning: "before:bg-orange-500 bg-orange-50",
          error: "before:bg-destructive bg-[#fcebeb]",
          default: "before:bg-gray-500 bg-gray-50"
        };
        return `${defaultClasses} ${baseClasses} ${typeClasses[context?.type || 'default']}`;
      }}
      {...rest}
    />
  );
}
