import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { RouterProvider } from "react-router";

import { router } from "./router";
import { QueryProvider } from "@/shared/providers/query-provider";
import { AppToastContainer } from "@/shared/components/toast/app-toast-container";

// TODO: Aplicar estilos Urbix cuando este implementado correctamente
import "./index.css";

const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);

root.render(
  <React.StrictMode>
    <QueryProvider>
      <RouterProvider router={router} />
      <AppToastContainer />
    </QueryProvider>
  </React.StrictMode>
);
