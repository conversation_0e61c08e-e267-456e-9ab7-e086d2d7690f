import { ButtonWithIcon } from "@components/button/button-with-icon";
import type { ReactNode } from "react";
import { MdArrowBack } from "react-icons/md";
import { useNavigate } from "react-router";

type ModuleHeaderProps = {
  title: string;
  hasBack?: boolean;
  children?: ReactNode;
};

export function ModuleHeader({ title, hasBack, children }: ModuleHeaderProps) {
  const navigate = useNavigate();

  return (
    <div className="w-full flex justify-between items-center gap-6 mb-4">
      <div className="flex items-center gap-1">
        {hasBack && (
          <ButtonWithIcon
            icon={<MdArrowBack />}
            variant="link"
            onClick={() => navigate(-1)}
          />
        )}
        <h1 className="text-xl font-semibold text-dark-gray-1000">{title}</h1>
      </div>
      <section className="flex gap-6">{children}</section>
    </div>
  );
}
