import React from 'react';
import { Switch } from '@/shared/components/ui/switch';
import { cn } from '@/styles/cn';
import { FilterMode } from '../types/advanced-date-filter.types';

interface DateFilterModeToggleProps {
  mode: FilterMode;
  onModeChange: (mode: FilterMode) => void;
  disabled?: boolean;
  className?: string;
}

export const DateFilterModeToggle: React.FC<DateFilterModeToggleProps> = ({
  mode,
  onModeChange,
  disabled = false,
  className
}) => {
  const handleToggle = (checked: boolean) => {
    onModeChange(checked ? 'free-selection' : 'complete-periods');
  };

  return (
    <div className={cn("flex flex-col space-y-3 p-4 bg-gray-50 rounded-lg", className)}>
      <div className="flex items-center justify-between">
        <div className="flex flex-col">
          <span className="text-sm font-medium text-gray-900">
            <PERSON><PERSON> Selección
          </span>
          <span className="text-xs text-gray-500">
            {mode === 'complete-periods' 
              ? 'Períodos completos (meses enteros)'
              : 'Selección libre (días específicos)'
            }
          </span>
        </div>
        
        <Switch
          checked={mode === 'free-selection'}
          onCheckedChange={handleToggle}
          disabled={disabled}
          aria-label="Alternar modo de selección de fechas"
        />
      </div>
      
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div className={cn(
          "p-2 rounded border transition-colors",
          mode === 'complete-periods' 
            ? "bg-primary-50 border-primary-200 text-primary-800" 
            : "bg-white border-gray-200 text-gray-600"
        )}>
          <div className="font-medium">Períodos Completos</div>
          <div className="text-xs opacity-75">Meses enteros</div>
        </div>
        
        <div className={cn(
          "p-2 rounded border transition-colors",
          mode === 'free-selection' 
            ? "bg-primary-50 border-primary-200 text-primary-800" 
            : "bg-white border-gray-200 text-gray-600"
        )}>
          <div className="font-medium">Selección Libre</div>
          <div className="text-xs opacity-75">Días específicos</div>
        </div>
      </div>
      
      {/* Mode description */}
      <div className="text-xs text-gray-600 bg-white p-2 rounded border">
        {mode === 'complete-periods' ? (
          <>
            <strong>Períodos Completos:</strong> Selecciona meses completos para análisis 
            de períodos regulares. Ideal para reportes mensuales y comparaciones por período.
          </>
        ) : (
          <>
            <strong>Selección Libre:</strong> Selecciona días específicos o rangos personalizados. 
            Ideal para análisis de eventos específicos o períodos irregulares.
          </>
        )}
      </div>
    </div>
  );
};

DateFilterModeToggle.displayName = 'DateFilterModeToggle';