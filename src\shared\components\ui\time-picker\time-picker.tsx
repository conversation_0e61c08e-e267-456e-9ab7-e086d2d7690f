import { TimePickerInput } from "./time-picker-input";
import { useRef } from "react";
import { Label } from "@components/label";
import { cn } from "@styles/cn";

interface TimePickerDemoProps {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  withSeconds?: boolean;
  title?: string;
  showLabels?: boolean;
  className?: string;
  required?: boolean;
}

export function TimePicker({
  date,
  setDate,
  withSeconds = false,
  title,
  showLabels = true,
  className,
  required = false,
}: TimePickerDemoProps) {
  const minuteRef = useRef<HTMLInputElement>(null);
  const hourRef = useRef<HTMLInputElement>(null);
  const secondRef = useRef<HTMLInputElement>(null);

  return (
    <div className={cn("flex flex-col gap-2", className)}>
      {title && (
        <Label>
          {title}
          {required && <span className="text-red-500"> *</span>}
        </Label>
      )}
      <div className="flex items-end gap-0">
        <div className="grid gap-1 text-center">
          {showLabels && (
            <Label htmlFor="hours" className="text-xs">
              HH
            </Label>
          )}
          <TimePickerInput
            className={cn(
              "rounded-l-md rounded-r-none border-r-1 border-neutral-600",
              className
            )}
            picker="hours"
            date={date}
            setDate={setDate}
            ref={hourRef}
            placeholder="HH"
            onRightFocus={() => minuteRef.current?.focus()}
          />
        </div>
        <div className="grid gap-1 text-center">
          {showLabels && (
            <Label htmlFor="minutes">
              MM
            </Label>
          )}
          <TimePickerInput
            className={cn("rounded-r-md rounded-l-none border-l-1", className)}
            picker="minutes"
            date={date}
            setDate={setDate}
            ref={minuteRef}
            placeholder="mm"
            onLeftFocus={() => hourRef.current?.focus()}
            onRightFocus={() => secondRef.current?.focus()}
          />
        </div>
        {withSeconds && (
          <div className="grid gap-1 text-center">
            {showLabels && (
              <Label htmlFor="seconds">
                SS
              </Label>
            )}
            <TimePickerInput
              picker="seconds"
              date={date}
              setDate={setDate}
              ref={secondRef}
              placeholder="ss"
              onLeftFocus={() => minuteRef.current?.focus()}
            />
          </div>
        )}
      </div>
    </div>
  );
}
