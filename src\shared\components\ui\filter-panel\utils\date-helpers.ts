import { isValid } from "date-fns";

/**
 * Combina una fecha con una cadena de tiempo para crear una cadena de fecha y hora formateada
 * @param date - El objeto fecha
 * @param timeString - Hora en formato HH:mm
 * @returns Cadena ISO o null si es inválido
 */
export const combineDateAndTime = (
  date: Date | null,
  timeString: string | null
): string | null => {
  if (!date || !timeString) return null;

  try {
    // Parseo la hora en formato HH:mm
    const timeParts = timeString.split(":");
    if (timeParts.length !== 2) return null;

    const hours = parseInt(timeParts[0], 10);
    const minutes = parseInt(timeParts[1], 10);

    if (
      isNaN(hours) ||
      isNaN(minutes) ||
      hours < 0 ||
      hours > 23 ||
      minutes < 0 ||
      minutes > 59
    ) {
      return null;
    }

    // Creo una nueva fecha con la hora especificada
    const combinedDate = new Date(date);
    combinedDate.setHours(hours, minutes, 0, 0);

    if (!isValid(combinedDate)) return null;

    // Retorno formato ISO
    return combinedDate.toISOString();
  } catch (error) {
    console.error("Error combining date and time:", error);
    return null;
  }
};

/**
 * Crea los parámetros de tiempo de inicio y fin desde el rango de fechas
 * @param dateRange - El rango de fechas
 * @returns Los parámetros de tiempo de inicio y fin
 */
export const createTimeParams = (dateRange: {
  selectedDate: Date | null;
  timeFrom: string | null;
  timeTo: string | null;
}) => {
  const { selectedDate, timeFrom, timeTo } = dateRange;

  const startTime = combineDateAndTime(selectedDate, timeFrom);
  const endTime = combineDateAndTime(selectedDate, timeTo);

  return {
    startTime,
    endTime,
  };
};
