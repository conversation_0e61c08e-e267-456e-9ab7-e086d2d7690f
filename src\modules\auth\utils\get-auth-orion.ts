import { post } from "@/shared/services/api/common";
import { orionServiceAxiosInstance } from "@/shared/services/axios";

export type OrionLoginResponse = {
    id:                 number;
    name:               string;
    userName:           string;
    role:               string;
    token:              string;
    legacyToken:        string;
    legacyRefreshToken: string;
};

export type OrionLoginRequest = {
    user: string;
    password: string;
};

export const getAuthOrion = (request: OrionLoginRequest) =>
    <TMappedOutput>(mapper: (response: OrionLoginResponse) => TMappedOutput) =>
        async (): Promise<TMappedOutput> => {
        return mapper(
            await post<OrionLoginRequest, OrionLoginResponse>(
                orionServiceAxiosInstance,
                '/security/authentication/login',
                request
            )
        )
    };