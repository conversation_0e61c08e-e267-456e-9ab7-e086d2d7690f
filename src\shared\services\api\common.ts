import { type AxiosInstance, isAxiosError } from "axios";

const getAuthHeader = async () => {
  const jwt = null;

  return {
    ...(jwt != null && { Authorization: `Bearer ${jwt}` }),
  };
};

const mapError = (error: unknown) => {
  if (isAxiosError<{ [key: string]: unknown }>(error)) {
    if (error.response) {
      // The request was made and the server responded with a status code
      if ((error.response.data.canOverride as boolean | undefined) ?? false) {
        return {
          type: "NeedsConfirmationError",
          message:
            (error.response.data.message as string | undefined) ??
            "Server Response without message",
        };
      }

      return {
        type: "ServiceError",
        statusCode: error.response.status,
        message:
          (error.response.data.message as string | undefined) ??
          "Server Response without message",
      };
    } else if (error.request != null) {
      // The request was made but no response was received
      return { type: "NetworkError", message: error.message, code: error.code };
    }
  }

  if (error instanceof Error) {
    return { type: "OtherError", message: error.message };
  }

  if (typeof error == "string") {
    return { type: "UnknownError", message: error };
  }

  return { type: "UnknownError", message: "unknown error" };
};

export const getWithParams = async <TRequest, TResponse>(
  axios: AxiosInstance,
  url: string,
  params: TRequest,
  token?: string,
  withCredentials?: boolean
): Promise<TResponse> => {
  try {
    const { data } = await axios.get<TResponse>(url, {
      params,
      withCredentials: withCredentials ?? !token ? true : false,
      headers: {
        Authorization: token && `Bearer ${token}`,
      },
    });

    return data;
  } catch (error) {
    return await Promise.reject(mapError(error));
  }
};

export const get = async <TResponse>({
  axios,
  url,
  token,
  withCredentials,
  params = {} as Record<string, string>,
}: {
  axios: AxiosInstance;
  url: string;
  token?: string;
  withCredentials?: boolean;
  params?: Record<string, string>;
}): Promise<TResponse> =>
  await getWithParams<object, TResponse>(
    axios,
    url,
    params,
    token,
    withCredentials
  );

export const post = async <TRequest = void, TResponse = void>(
  axios: AxiosInstance,
  url: string,
  body?: TRequest
): Promise<TResponse> => {
  try {
    const { data } = await axios.post<TResponse>(url, body, {
      headers: {
        "Content-Type": "application/json",
        ...(await getAuthHeader()),
      },
    });

    return data;
  } catch (error) {
    return await Promise.reject(mapError(error));
  }
};

export const put = async <TRequest = void, TResponse = void>(
  axios: AxiosInstance,
  url: string,
  body?: TRequest
): Promise<TResponse> => {
  try {
    const { data } = await axios.put<TResponse>(url, body, {
      headers: {
        "Content-Type": "application/json",
        ...(await getAuthHeader()),
      },
    });

    return data;
  } catch (error) {
    return await Promise.reject(mapError(error));
  }
};

export const deleteWithParams = async <TRequest, TResponse>(
  axios: AxiosInstance,
  url: string,
  params: TRequest
): Promise<TResponse> => {
  try {
    const { data } = await axios.delete<TResponse>(url, {
      params,
      headers: {
        ...(await getAuthHeader()),
      },
    });

    return data;
  } catch (error) {
    return await Promise.reject(mapError(error));
  }
};
