import { useMutation } from "@tanstack/react-query";
import { postMediaRequest } from "@services/api/post-media-request";
import { queryClient } from "@/shared/providers/query-client";
import { useToast } from "@/shared/components/toast/hooks/use-toast";

export function useCreateClip() {
  const { showToast } = useToast();

  return useMutation({
    mutationFn: postMediaRequest,
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["videos"],
      });

      const title = variables.messageDescription;
      showToast({
        type: "success",
        theme: "light",
        title: "Clip de video solicitado",
        description: `El clip “${title}” se encuentra pendiente de descarga.`,
      });
    },
    onError: (error, variables) => {
      const title = variables.messageDescription;
      showToast({
        type: "error",
        theme: "light",
        title: "Error al solicitar el clip",
        description: `El clip “${title}” no se pudo crear.`,
      });

      console.error("Failed to create clip:", error);
    },
  });
}
