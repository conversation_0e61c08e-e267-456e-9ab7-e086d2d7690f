import {billingServiceAxiosInstance} from "@/shared";
import {get} from "@/shared/services/api";

export type PlansResponse = {
  id: number;
  exCompanyId: number;
  name: string;
  type: string;
  state: string;
  telco: string;
  vehicles: number;
  clearing: boolean;
  download: {
    total: number;
    limit: number;
  };
  view: {
    total: number;
  };
  storage: {
    retentionPolicy: number;
    total: number;
  };
};

export const plansApi = {
  getAll:
    <TMappedOutput>(mapper: (response: PlansResponse[]) => TMappedOutput, token: string) =>
    async (companyId: number): Promise<TMappedOutput> => {
      const response = await get<PlansResponse[]>(
        billingServiceAxiosInstance,
        `/api/company/${companyId}/plans`,
        token,
      );

      return mapper(response);
    },
};
