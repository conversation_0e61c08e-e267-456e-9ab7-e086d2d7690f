import type { ReactNode } from "react";
import {
  <PERSON>er,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerFooter,
} from "./drawer-primitives";

type UrbeDrawerProps = {
  trigger: ReactNode;
  title: string;
  description?: string;
  footer?: ReactNode;
  children?: ReactNode;
  onOpenChange?: (open: boolean) => void;
  open?: boolean;
};

export function UrbeDrawer({
  trigger,
  title,
  footer,
  children,
  onOpenChange,
  open,
}: UrbeDrawerProps) {
  return (
    <Drawer direction="right" onOpenChange={onOpenChange} open={open}>
      <DrawerTrigger asChild>{trigger}</DrawerTrigger>
      <DrawerContent>
        <DrawerHeader title={title} />
        <div className="bg-white h-full w-full grow p-6 flex flex-col overflow-y-auto">
          {children}
        </div>
        <DrawerFooter className="bg-white">{footer}</DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
