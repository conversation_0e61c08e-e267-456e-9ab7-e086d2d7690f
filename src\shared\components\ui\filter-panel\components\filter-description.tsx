import { ReactElement } from "react";
import {
  usePendingCompany,
  usePendingVehicle,
  usePendingDateRange,
} from "@components/filter-panel/stores/filter-store";
import { usePendingFilterValue } from "@components/filter-panel/stores/dynamic-filter-store";
import { combineDateAndTime } from "../utils/date-helpers";
import { findOptionLabel } from "../utils/find-option-label";
import { format } from "date-fns";


export function FilterDescription({
  componentName,
  child,
  dynamicLabel,
}: {
  componentName: string;
  child: ReactElement;
  dynamicLabel?: string;
}) {
  const pendingCompany = usePendingCompany();
  const pendingVehicle = usePendingVehicle();
  const { selectedDate, timeFrom, timeTo } = usePendingDateRange();

  const pendingDynamicValue = usePendingFilterValue<string | string[]>(
    componentName === "DynamicFilter" ? child.props?.filterKey || "" : ""
  );

  const childOptions = child.props?.options;

  if (componentName === "CompanyFilter" && pendingCompany) {
    const label = findOptionLabel(childOptions, pendingCompany);
    if (label) {
      return (
        <div className="text-xs text-primary-800 font-medium">{label}</div>
      );
    }
  }

  if (componentName === "VehicleFilter" && pendingVehicle) {
    const label = findOptionLabel(childOptions, pendingVehicle);
    if (label) {
      return (
        <div className="text-xs text-primary-800 font-medium">{label}</div>
      );
    }
  }

  if (componentName === "DateFilter" && selectedDate) {
    const combinedDate = combineDateAndTime(selectedDate, timeFrom);
    const combinedDateTo = combineDateAndTime(selectedDate, timeTo);
    const formattedLabel = combinedDate
      ? format(combinedDate, "dd/MM/yyyy HH:mm")
      : "";
    const formattedLabelTo = combinedDateTo
      ? format(combinedDateTo, "dd/MM/yyyy HH:mm")
      : "";
    const label = `${formattedLabel} - ${formattedLabelTo}`;
    return <div className="text-xs text-primary-800 font-medium">{label}</div>;
  }

  if (
    componentName === "DynamicFilter" &&
    pendingDynamicValue &&
    childOptions
  ) {
    if (Array.isArray(pendingDynamicValue) && pendingDynamicValue.length > 0) {
      const labels = pendingDynamicValue
        .map((value) => findOptionLabel(childOptions, value))
        .filter(Boolean);
      if (labels.length > 0) {
        const maxShow = 3;
        const visibleLabels = labels.slice(0, maxShow);
        const remainingCount = labels.length - maxShow;

        return (
          <div className="text-xs text-primary-800 font-medium truncate">
            {visibleLabels.join("; ")}
            {remainingCount > 0 && ` +${remainingCount}`}
          </div>
        );
      }
    } else if (typeof pendingDynamicValue === "string") {
      const label = findOptionLabel(childOptions, pendingDynamicValue);
      if (label) {
        return (
          <div className="text-xs text-primary-800 font-medium">{label}</div>
        );
      }
    }
  }

  return (
    <div className="text-xs text-neutral-800 font-medium">
      {componentName === "CompanyFilter" && "Selecciona un distrito"}
      {componentName === "VehicleFilter" && "Selecciona un vehículo"}
      {componentName === "DateFilter" && "Selecciona una fecha o período"}
      {componentName === "DynamicFilter" &&
        `Selecciona ${dynamicLabel?.toLowerCase() || "opciones"}`}
    </div>
  );
}

FilterDescription.displayName = "FilterDescription";
