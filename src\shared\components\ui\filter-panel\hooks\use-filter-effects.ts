import { useEffect, type ReactElement, type RefObject } from "react";

type UseFilterEffectsProps = {
  isDrawerOpen: boolean;
  selectedFilter: ReactElement | null;
  setSelectedFilter: (filter: ReactElement | null) => void;
  setIsDrawerOpen: (open: boolean) => void;
  drawerRef: RefObject<HTMLDivElement>;
  miniDrawerRef: RefObject<HTMLDivElement>;
  clearStaticPending: () => void;
  clearDynamicPending: () => void;
  initializeStaticPending: () => void;
  initializePendingWithApplied: () => void;
};

export function useFilterEffects({
  isDrawerOpen,
  selectedFilter,
  setSelectedFilter,
  setIsDrawerOpen,
  drawerRef,
  miniDrawerRef,
  clearStaticPending,
  clearDynamicPending,
  initializeStaticPending,
  initializePendingWithApplied,
}: UseFilterEffectsProps) {
  useEffect(() => {
    if (!isDrawerOpen) {
      setSelectedFilter(null);
      clearStaticPending();
      clearDynamicPending();
    } else {
      initializeStaticPending();
      initializePendingWithApplied();
    }
  }, [
    isDrawerOpen,
    setSelectedFilter,
    clearStaticPending,
    clearDynamicPending,
    initializeStaticPending,
    initializePendingWithApplied,
  ]);

  useEffect(() => {
    function handleGlobalClick(event: MouseEvent) {
      const target = event.target as HTMLElement;

      const clickedMainDrawer = drawerRef.current?.contains(target);
      const clickedMiniDrawer = miniDrawerRef.current?.contains(target);

      if (!clickedMainDrawer && !clickedMiniDrawer) {
        setSelectedFilter(null);
        setIsDrawerOpen(false);
      } else if (selectedFilter && !clickedMiniDrawer && clickedMainDrawer) {
        setSelectedFilter(null);
      }
    }

    if (isDrawerOpen) {
      document.addEventListener("click", handleGlobalClick);
    }

    return () => {
      document.removeEventListener("click", handleGlobalClick);
    };
  }, [
    isDrawerOpen,
    selectedFilter,
    setSelectedFilter,
    setIsDrawerOpen,
    drawerRef,
    miniDrawerRef,
  ]);
}
