import { ChevronDown, ChevronUp } from "lucide-react";
import { useFormContext } from "react-hook-form";
import { Popover, PopoverContent, PopoverTrigger } from "@components/popover";
import { Button } from "@components/button";

import type { ReactNode } from "react";
import { useEffect, useState } from "react";
import { cn } from "@styles/cn";
import { Calendar } from "../calendar";
import { useFormRulesContext } from "@/shared/forms/form-rules";

export type DatePickerProps = {
  name: string;
  placeholder?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  label?: string;
  required?: boolean;
  hasError?: boolean;
  isDisabled?: boolean;
  isReadonly?: boolean;
  isLoading?: boolean;
  hasEmptyValue?: boolean;
  className?: string;
};

export const DatePicker = ({
  name,
  placeholder = "Seleccionar fecha...",
  leftIcon,
  rightIcon,
  label,
  required = false,
  hasError = false,
  isDisabled = false,
  isReadonly = false,
  isLoading = false,
  className,
}: DatePickerProps) => {
  const [open, setOpen] = useState(false);
  const {
    register,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();
  const rule = useFormRulesContext();

  const selectedValue = watch(name);

  useEffect(() => {
    register(name, rule(name));
  }, [register, name, rule]);

  const handleSelect = (date: Date | undefined) => {
    if (date) {
      // Format date in local timezone to avoid timezone offset issues
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const formattedDate = `${year}-${month}-${day}`;
      setValue(name, formattedDate, { shouldValidate: true });
    }
    setOpen(false);
  };

  const selectedDate = selectedValue
    ? new Date(selectedValue + "T00:00:00")
    : undefined;

  return (
    <div className={cn("w-full", className)}>
      {label && (
        <label
          className="block text-sm font-semibold text-gray-700 mb-2"
          htmlFor={name}
        >
          {label}
          {required && <span className="text-red-500"> *</span>}
        </label>
      )}

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="link"
            role="combobox"
            aria-expanded={open}
            disabled={isDisabled}
            className={cn(
              "w-full justify-between border-[1px] border-gray-500 bg-white hover:bg-gray-50 p-3 rounded-md focus:ring-2 focus:ring-primary-600 focus:border-primary-600",
              open && "border-primary-600 ring-2 ring-primary-600",
              (hasError || errors[name]) && "border-red-500",
              isReadonly && "bg-gray-100 cursor-default",
              leftIcon && "pl-3",
              rightIcon && "pr-3"
            )}
          >
            <div className="flex items-center w-full">
              {leftIcon && <div className="mr-2 flex-shrink-0">{leftIcon}</div>}
              <div className="flex-1 text-left">
                {isLoading ? (
                  "Cargando..."
                ) : selectedDate ? (
                  <span className="text-gray-900">
                    {selectedDate.toLocaleDateString("es-ES", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </span>
                ) : (
                  <span className="text-gray-500">{placeholder}</span>
                )}
              </div>
              {rightIcon && (
                <div className="ml-2 flex-shrink-0">{rightIcon}</div>
              )}
              {open ? (
                <ChevronUp className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              ) : (
                <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              )}
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleSelect}
            className="rounded-md border shadow-sm"
            disabled={{
              after: new Date(),
            }}
            defaultMonth={new Date()}
            required
          />
        </PopoverContent>
      </Popover>

      {errors[name] && (
        <p className="text-red-500 text-sm mt-1">
          {errors[name]?.message as string}
        </p>
      )}
    </div>
  );
};
