# Filter System Usage Guide

This guide shows how to use the FilterPanel component library in your applications.

## 📦 Installation & Setup

```bash
npm install @urbetrack/urbix
yarn add @urbetrack/urbix
pnpm add @urbetrack/urbix
```

```tsx
// Required imports for basic usage
import {
  FilterPanel,
  CompanyFilter,
  DynamicFilter,
  PlanFilter,
  // Static filter hooks
  useAppliedCompany,
  usePendingCompany,
  useActiveFiltersCount,
  useStaticHasPendingChanges,
  // Dynamic filter hooks
  useAppliedFilterValue,
  usePendingFilterValue,
  useDynamicActiveFiltersCount,
  useDynamicHasPendingChanges,
  useSetPendingFilter,
} from "@urbetrack/urbix";
```

## 🚀 Basic Usage

### 1. Simple FilterPanel with Core Filters

```tsx
import { FilterPanel, CompanyFilter, DynamicFilter } from "@urbetrack/urbix";

export const MyDataView = () => {
  const statusOptions = [
    { id: 1, text: "Active", disabled: false },
    { id: 2, text: "Inactive", disabled: false },
  ];

  return (
    <div>
      <FilterPanel>
        <CompanyFilter
          companiesLoading={false}
          companiesOptions={companyData}
          moveSelectedToTop={true}
        />
        <DynamicFilter
          filterKey="status"
          label="Status"
          type="select"
          options={statusOptions}
          placeholder="Select status"
          searchable={true}
        />
      </FilterPanel>

      {/* Your data content here */}
      <MyDataTable />
    </div>
  );
};
```

### 2. Accessing Filter Values for Data Fetching

```tsx
import {
  useAppliedCompany,
  useAppliedFilterValue,
  useActiveFiltersCount,
  useDynamicActiveFiltersCount,
} from "@urbetrack/urbix";

export const MyDataContainer = () => {
  // Get applied filter values (only these should be used for data fetching)
  const selectedCompany = useAppliedCompany();
  const selectedStatus = useAppliedFilterValue<string>("status");
  const selectedCategories = useAppliedFilterValue<string[]>("categories");

  // Get active filters count
  const staticActiveFiltersCount = useActiveFiltersCount();
  const dynamicActiveFiltersCount = useDynamicActiveFiltersCount();
  const totalActiveFilters =
    staticActiveFiltersCount + dynamicActiveFiltersCount;

  // Use in data fetching
  const { data, loading } = useQuery(GET_DATA, {
    variables: {
      companyId: selectedCompany ? parseInt(selectedCompany) : null,
      status: selectedStatus,
      categories: selectedCategories,
    },
    // Refetch when filters change
    skip: !selectedCompany,
  });

  return (
    <div>
      <FilterPanel>{/* Your filters here */}</FilterPanel>

      {loading ? <Loading /> : <DataTable data={data} />}

      {totalActiveFilters > 0 && (
        <div>Active filters: {totalActiveFilters}</div>
      )}
    </div>
  );
};
```

### 3. Tanstack React Query Integration

```tsx
import { useQuery } from "@tanstack/react-query";
import { useAppliedCompany, useAppliedFilterValue } from "@urbetrack/urbix";

export const useFilteredData = () => {
  const companyId = useAppliedCompany();
  const status = useAppliedFilterValue<string>("status");
  const priority = useAppliedFilterValue<string[]>("priority");

  return useQuery({
    queryKey: ["data", companyId, status, priority],
    queryFn: () =>
      fetchData({
        companyId: companyId ? parseInt(companyId) : undefined,
        status,
        priority,
      }),
    enabled: !!companyId, // Only fetch when company is selected
  });
};

// Usage in component
export const MyComponent = () => {
  const { data, isLoading, error } = useFilteredData();

  return (
    <div>
      <FilterPanel>{/* filters */}</FilterPanel>
      {/* render data */}
    </div>
  );
};
```

## 🎯 Advanced Usage

### 4. Adding Custom Dynamic Filters with Dependencies

```tsx
export const AdvancedFilters = () => {
  const priorityOptions = [
    { id: 1, text: "High", disabled: false },
    { id: 2, text: "Medium", disabled: false },
    { id: 3, text: "Low", disabled: false },
  ];

  const categoryOptions = [
    { id: 1, text: "Safety", disabled: false },
    { id: 2, text: "Traffic", disabled: false },
    { id: 3, text: "Maintenance", disabled: false },
  ];

  return (
    <FilterPanel>
      {/* Core static filter */}
      <CompanyFilter companiesLoading={false} companiesOptions={companies} />

      {/* Plan filter depends on company selection */}
      <PlanFilter
        planLoading={false}
        planOptions={planOptions}
        dependsOn="company"
        moveSelectedToTop={true}
      />

      {/* Dynamic filters */}
      <DynamicFilter
        searchable
        filterKey="priority"
        label="Priority"
        type="multiselect"
        options={priorityOptions}
        placeholder="Select priorities"
        moveSelectedToTop={true}
      />

      <DynamicFilter
        filterKey="category"
        label="Category"
        type="select"
        options={categoryOptions}
        placeholder="Select category"
        dependsOn="company"
      />

      {/* Filter that depends on other dynamic filters */}
      <DynamicFilter
        searchable
        filterKey="subcategory"
        label="Subcategory"
        type="multiselect"
        options={subcategoryOptions}
        placeholder="Select subcategories"
        dependsOn={["company", "category"]}
      />
    </FilterPanel>
  );
};
```

### 5. Custom Event Handlers & Pending Changes

```tsx
export const MyApp = () => {
  const staticHasPendingChanges = useStaticHasPendingChanges();
  const dynamicHasPendingChanges = useDynamicHasPendingChanges();
  const hasPendingChanges = staticHasPendingChanges || dynamicHasPendingChanges;

  const handleFiltersCleared = () => {
    console.log("All filters cleared");
    // Reset any local state if needed
  };

  const handleFiltersApplied = () => {
    console.log("Filters applied");
    // Trigger analytics, etc.
  };

  return (
    <div>
      <FilterPanel
        onClearFilters={handleFiltersCleared}
        onApplyFilters={handleFiltersApplied}
      >
        {/* filters */}
      </FilterPanel>

      {hasPendingChanges && (
        <div className="bg-orange-100 p-2 text-sm">
          ⚠️ You have pending filter changes. Click "Apply" to activate them.
        </div>
      )}
    </div>
  );
};
```

### 6. Monitoring Pending vs Applied States

```tsx
export const FilterStateMonitor = () => {
  // Pending states (what user is currently selecting)
  const pendingCompany = usePendingCompany();
  const pendingStatus = usePendingFilterValue<string>("status");

  // Applied states (what's actually active for data fetching)
  const appliedCompany = useAppliedCompany();
  const appliedStatus = useAppliedFilterValue<string>("status");

  // Change detection
  const staticHasPendingChanges = useStaticHasPendingChanges();
  const dynamicHasPendingChanges = useDynamicHasPendingChanges();

  return (
    <div className="space-y-2 text-sm">
      <div>
        <strong>Company:</strong>
        <div className="ml-4">
          <div>✏️ Selecting: {pendingCompany || "None"}</div>
          <div>✅ Applied: {appliedCompany || "None"}</div>
        </div>
      </div>

      <div>
        <strong>Status:</strong>
        <div className="ml-4">
          <div>✏️ Selecting: {pendingStatus || "None"}</div>
          <div>✅ Applied: {appliedStatus || "None"}</div>
        </div>
      </div>

      <div>
        <strong>Changes:</strong>
        <div className="ml-4">
          <div>Static filters: {staticHasPendingChanges ? "Yes" : "No"}</div>
          <div>Dynamic filters: {dynamicHasPendingChanges ? "Yes" : "No"}</div>
        </div>
      </div>
    </div>
  );
};
```

## 🔧 Integration Patterns

### 7. With Existing State Management

```tsx
// If you need to sync with Redux/Zustand
export const StateSyncExample = () => {
  const companyId = useAppliedCompany();
  const dispatch = useAppDispatch();

  // Sync filter changes to your existing store
  useEffect(() => {
    if (companyId) {
      dispatch(setSelectedCompany(companyId));
    }
  }, [companyId, dispatch]);

  return <FilterPanel>{/* filters */}</FilterPanel>;
};
```

### 8. URL Synchronization

```tsx
import { useSearchParams } from "react-router-dom";

export const URLSyncFilters = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const companyId = useAppliedCompany();
  const status = useAppliedFilterValue<string>("status");

  // Sync applied filters to URL
  useEffect(() => {
    const params = new URLSearchParams(searchParams);

    if (companyId) {
      params.set("company", companyId);
    } else {
      params.delete("company");
    }

    if (status) {
      params.set("status", status);
    } else {
      params.delete("status");
    }

    setSearchParams(params);
  }, [companyId, status, searchParams, setSearchParams]);

  return <FilterPanel>{/* filters */}</FilterPanel>;
};
```

### 9. Programmatic Filter Control

```tsx
import { useSetPendingFilter, useDynamicFilterStore } from "@urbetrack/urbix";

export const ProgrammaticFilters = () => {
  const setPendingFilter = useSetPendingFilter();
  const applyFilters = useDynamicFilterStore(
    (state) => state.applyPendingFilters
  );

  const handleQuickFilter = (preset: string) => {
    switch (preset) {
      case "high-priority":
        setPendingFilter("priority", ["1"]); // High priority
        setPendingFilter("status", "active");
        break;
      case "safety-issues":
        setPendingFilter("category", "safety");
        setPendingFilter("priority", ["1", "2"]); // High and Medium
        break;
    }

    // Apply the changes immediately
    applyFilters();
  };

  return (
    <div>
      <div className="mb-4 space-x-2">
        <button onClick={() => handleQuickFilter("high-priority")}>
          High Priority Items
        </button>
        <button onClick={() => handleQuickFilter("safety-issues")}>
          Safety Issues
        </button>
      </div>

      <FilterPanel>{/* filters */}</FilterPanel>
    </div>
  );
};
```

## 📋 Available Hooks Reference

### Static Filter Hooks

```tsx
// Applied values (use these for data fetching)
useAppliedCompany() → string | null
useAppliedPlan() → string | null
useAppliedVehicles() → string[]
useAppliedCycle() → string

// Pending values (what user is currently selecting)
usePendingCompany() → string | null
usePendingPlan() → string | null
usePendingVehicles() → string[]
usePendingCycle() → string

// State checks
useActiveFiltersCount() → number
useHasActiveFilters() → boolean
useStaticHasPendingChanges() → boolean
```

### Dynamic Filter Hooks

```tsx
// Applied values (use these for data fetching)
useAppliedFilterValue<T>(key: string) → T | undefined

// Pending values (what user is currently selecting)
usePendingFilterValue<T>(key: string) → T | undefined

// Actions
useSetPendingFilter() → (key: string, value: FilterValue) => void
useApplyPendingFilters() → () => void
useRegisterFilter() → (key: string, metadata: FilterMetadata) => void

// State checks
useDynamicActiveFiltersCount() → number
useDynamicHasActiveFilters() → boolean
useDynamicHasPendingChanges() → boolean
```

### Store Actions (Advanced)

```tsx
// Direct store access (use sparingly)
useFilterStore((state) => state.setPendingCompany);
useFilterStore((state) => state.clearAllFilters);
useDynamicFilterStore((state) => state.clearAllFilters);
```

## 🎨 Component Props Reference

### FilterPanel

```tsx
interface FilterPanelProps {
  children: ReactNode;
  onClearFilters?: () => void;
  onApplyFilters?: () => void;
}
```

### CompanyFilter

```tsx
interface CompanyFilterProps {
  companiesLoading: boolean;
  companiesOptions: DropdownType[];
  moveSelectedToTop?: boolean;
}
```

### PlanFilter

```tsx
interface PlanFilterProps {
  planLoading: boolean;
  planOptions: DropdownType[];
  dependsOn?: string | string[]; // e.g., "company" or ["company", "region"]
  moveSelectedToTop?: boolean;
}
```

### DynamicFilter

```tsx
interface DynamicFilterProps {
  filterKey: string; // Unique identifier
  label: string; // Display name
  type: "select" | "multiselect"; // UI type (text, date, etc. not yet implemented)
  options?: DropdownType[]; // Available options
  placeholder?: string; // Input placeholder
  searchable?: boolean; // Enable search functionality
  dependsOn?: string | string[]; // Dependencies on other filters
  moveSelectedToTop?: boolean; // Move selected items to top of list
}
```

### DropdownType

```tsx
interface DropdownType {
  id: string | number;
  text: string;
  disabled: boolean;
}
```

## ✨ Best Practices

### 🔄 Pending vs Applied State System

This filter system uses a **two-stage approach**:

- **Pending State**: What the user is currently selecting
- **Applied State**: What's actually active for data fetching

**Always use Applied values for data fetching:**

```tsx
// ✅ CORRECT - Use for data fetching
const companyId = useAppliedCompany();
const status = useAppliedFilterValue<string>("status");

// ❌ WRONG - Don't use pending values for data fetching
const pendingCompany = usePendingCompany(); // Only for UI display
```

### 🏗️ Component Usage

1. **Filter Keys**: Use consistent, descriptive keys for dynamic filters
2. **Dependencies**: Use `dependsOn` prop to create filter hierarchies
3. **Loading States**: Handle loading states when options are being fetched
4. **Performance**: Hooks are optimized - safe to use in multiple components
5. **Error Boundaries**: Wrap FilterPanel in error boundaries for production

### 🔗 Filter Dependencies

```tsx
// Simple dependency on company
<DynamicFilter dependsOn="company" />

// Multiple dependencies
<DynamicFilter dependsOn={["company", "region"]} />

// Built-in dependencies
<PlanFilter dependsOn="company" /> // Built-in dependency
```

### 🎯 Data Fetching Pattern

```tsx
export const useDataWithFilters = () => {
  // Only use applied values
  const companyId = useAppliedCompany();
  const status = useAppliedFilterValue<string>("status");

  return useQuery({
    queryKey: ["data", companyId, status],
    queryFn: () => fetchData({ companyId, status }),
    enabled: !!companyId, // Ensure required filters are set
  });
};
```

## 🚀 Quick Start Template

```tsx
import {
  FilterPanel,
  CompanyFilter,
  DynamicFilter,
  useAppliedCompany,
  useAppliedFilterValue,
} from "@urbetrack/urbix";

export const QuickStartExample = () => {
  // Get applied filter values for data fetching
  const companyId = useAppliedCompany();
  const status = useAppliedFilterValue<string>("status");

  // Your data fetching logic here
  const { data } = useMyData(companyId, status);

  const statusOptions = [
    { id: 1, text: "Active", disabled: false },
    { id: 2, text: "Inactive", disabled: false },
  ];

  return (
    <div>
      <FilterPanel>
        <CompanyFilter companiesLoading={false} companiesOptions={companies} />
        <DynamicFilter
          filterKey="status"
          label="Status"
          type="select"
          options={statusOptions}
          searchable
        />
      </FilterPanel>

      <MyDataTable data={data} />
    </div>
  );
};
```

## 🎨 Filter Types Available

### Currently Implemented

- **select**: Single selection with radio buttons
- **multiselect**: Multiple selection with checkboxes

### Soon

- **text**: Text input filters
- **date**: Date picker filters
- **daterange**: Date range picker filters
- **boolean**: Toggle/checkbox/switch filters
- **number**: Numeric input filters

The filter system is designed to be **zero-configuration** for basic usage, but **highly flexible** for advanced scenarios with full dependency management and pending/applied state control.
