import type { CheckedState } from "@radix-ui/react-checkbox";

import { Checkbox } from "../checkbox";
import { cn } from "@styles/cn";

type ListItemProps = {
  children: React.ReactNode;
  checkbox?: boolean;
  description?: string;
  secondaryDescription?: string;
  rightIcon?: React.ReactNode;
  leftIcon?: React.ReactNode;
  onClick?: () => void;
  onChecked?: (checked: CheckedState | string) => void;
  checked?: CheckedState;
  disabled?: boolean;
  className?: string;
  checkOnClick?: boolean;
};

export function ListItem({
  checkbox,
  children,
  description,
  secondaryDescription,
  checked,
  leftIcon,
  rightIcon,
  disabled,
  onClick,
  onChecked,
  className,
  checkOnClick,
}: ListItemProps) {
  return (
    <div className="relative flex w-64 gap-2 rounded p-2 hover:bg-gray-100">
      {checkbox && (
        <Checkbox
          onCheckedChange={onChecked}
          disabled={disabled}
          className="mt-[2px]"
          checked={checked}
        />
      )}
      {leftIcon && <div>{leftIcon}</div>}
      <button
        type="button"
        className="flex flex-1 flex-col text-left"
        onClick={() => {
          onClick?.();
          if (checkbox && checkOnClick) {
            onChecked?.(!checked);
          }
        }}
      >
        <div className="flex justify-between">
          <div className="flex flex-col">
            <span
              className={cn(
                "text-sm font-medium text-gray-1000",
                className,
                disabled && "text-gray-400"
              )}
            >
              {children}
            </span>
            <span
              className={cn(
                "text-xs font-medium text-gray-600",
                disabled && "text-gray-400"
              )}
            >
              {description}
            </span>
            <span
              className={cn(
                "text-xs font-medium text-gray-600",
                disabled && "text-gray-400"
              )}
            >
              {secondaryDescription}
            </span>
          </div>
        </div>
        <div className="absolute right-2 top-2">{rightIcon}</div>
      </button>
    </div>
  );
}
