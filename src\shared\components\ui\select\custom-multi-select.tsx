import { useFormContext, useWatch } from "react-hook-form";
import type { ReactNode } from "react";
import { useEffect, useState } from "react";
import { cn } from "@styles/cn";
import { Label } from "@components/label";
import { Button } from "@components/button";
import { Popover, PopoverContent, PopoverTrigger } from "@components/popover";
import { Check, ChevronDown, ChevronUp, X, Minus } from "lucide-react";
import { useFormRulesContext } from "@/shared/forms/form-rules";

type CustomMultiSelectId = number | string;

export type CustomMultiSelectOptionType<TExtra = Record<string, unknown>> = {
  id: CustomMultiSelectId;
  text: string;
  key?: string;
  disabled?: boolean;
  selected?: boolean;
  extra?: TExtra;
};

export type CustomMultiSelectProps<TExtra = Record<string, unknown>> = {
  name: string;
  options: CustomMultiSelectOptionType<TExtra>[];
  placeholder?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  label?: string;
  required?: boolean;
  hasError?: boolean;
  isDisabled?: boolean;
  isReadonly?: boolean;
  isLoading?: boolean;
  hasEmptyValue?: boolean;
  className?: string;
  dynamicLeftContent?: (
    option: CustomMultiSelectOptionType<TExtra>
  ) => ReactNode;
  showErrors?: boolean;
  textFormatter?: (text: string) => ReactNode;
  maxSelected?: number;
  showSelectAll?: boolean;
};

export const CustomMultiSelect = ({
  name,
  options,
  placeholder = "Select options...",
  leftIcon,
  rightIcon,
  label,
  required = false,
  isDisabled = false,
  isReadonly = false,
  isLoading = false,
  hasEmptyValue = false,
  className,
  dynamicLeftContent,
  showErrors = false,
  textFormatter,
  maxSelected,
  showSelectAll = true,
}: CustomMultiSelectProps) => {
  const [open, setOpen] = useState(false);
  const methods = useFormContext();
  const rule = useFormRulesContext();

  const selectedValues =
    (useWatch({
      name,
      control: methods.control,
    }) as CustomMultiSelectId[]) || [];

  const errors = showErrors ? methods.formState.errors : {};

  const isMultiSelectDisabled =
    isDisabled || isReadonly || options.length === 0;

  const selectedOptions = options.filter((option) =>
    selectedValues.includes(option.id)
  );

  const allSelected =
    options.length > 0 && selectedValues.length === options.length;
  const someSelected =
    selectedValues.length > 0 && selectedValues.length < options.length;

  const handleSelect = (optionId: CustomMultiSelectId) => {
    const newValues = selectedValues.includes(optionId)
      ? selectedValues.filter((id) => id !== optionId)
      : maxSelected && selectedValues.length >= maxSelected
      ? selectedValues
      : [...selectedValues, optionId];

    methods.setValue(name, newValues);
    if (showErrors) {
      methods.clearErrors(name);
    }
  };

  const handleSelectAll = () => {
    if (allSelected) {
      methods.setValue(name, []);
    } else {
      const allIds = options.map((option) => option.id);
      methods.setValue(name, allIds);
    }
    if (showErrors) {
      methods.clearErrors(name);
    }
  };

  const handleRemoveItem = (optionId: CustomMultiSelectId) => {
    const newValues = selectedValues.filter((id) => id !== optionId);
    methods.setValue(name, newValues);
    if (showErrors) {
      methods.clearErrors(name);
    }
  };

  const handleClearAll = () => {
    methods.setValue(name, []);
    if (showErrors) {
      methods.clearErrors(name);
    }
  };

  useEffect(() => {
    methods.register(name, rule(name));
  }, [methods, name, rule]);

  const renderTriggerContent = () => {
    if (isLoading) {
      return "Cargando...";
    }

    if (selectedOptions.length === 0) {
      return <span className="text-gray-800">{placeholder}</span>;
    }

    if (selectedOptions.length === 1) {
      const option = selectedOptions[0];
      return (
        <div className="flex items-center gap-1 flex-wrap">
          {dynamicLeftContent && dynamicLeftContent(option)}
          <div className="flex items-center gap-1 py-1 px-2 rounded-full text-xs text-primary-800 bg-primary-100 border border-primary-200">
            {textFormatter ? textFormatter(option.text) : option.text}
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveItem(option.id);
              }}
              className="hover:bg-primary-200 rounded-full p-0.5"
            >
              <X className="h-3 w-3" />
            </button>
          </div>
        </div>
      );
    }

    if (selectedOptions.length === 2) {
      return (
        <div className="flex items-center gap-1 flex-wrap">
          {selectedOptions.map((option) => (
            <div
              key={option.key ?? option.id}
              className="flex items-center gap-1 px-2 py-1 rounded-full text-xs text-primary-800 bg-primary-100 border border-primary-200"
            >
              {dynamicLeftContent && dynamicLeftContent(option)}
              {textFormatter ? textFormatter(option.text) : option.text}
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveItem(option.id);
                }}
                className="hover:bg-primary-200 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="flex items-center gap-1 flex-wrap">
        <div className="flex items-center gap-1 px-2 py-1 rounded-full text-xs text-primary-800 bg-primary-100 border border-primary-200">
          {textFormatter
            ? textFormatter(selectedOptions[0].text)
            : selectedOptions[0].text}
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              handleRemoveItem(selectedOptions[0].id);
            }}
            className="hover:bg-primary-200 rounded-full p-0.5"
          >
            <X className="h-3 w-3" />
          </button>
        </div>
        <div className="flex items-center px-2 py-1 rounded-full text-xs text-primary-800 bg-primary-100 border border-primary-200">
          +{selectedOptions.length - 1}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      {label && (
        <Label htmlFor={name}>
          {label}
          {required && <span className="text-red-500"> *</span>}
        </Label>
      )}

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="secondary"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between border hover:bg-neutral-50 pl-3",
              showErrors && errors[name]
                ? "border-red-500 disabled:border-red-500"
                : "border-gray-500",
              open &&
                !errors[name] &&
                "border-primary-600 ring-2 ring-primary-600",
              isReadonly && "bg-gray-100 cursor-default",
              leftIcon && "pl-3",
              rightIcon && "pr-3"
            )}
            disabled={isMultiSelectDisabled}
          >
            <div className="flex items-center w-full min-h-[20px]">
              {leftIcon && <div className="mr-2 flex-shrink-0">{leftIcon}</div>}
              <div className="flex-1 text-left">{renderTriggerContent()}</div>
              {rightIcon && (
                <div className="ml-2 flex-shrink-0">{rightIcon}</div>
              )}
              {open ? (
                <ChevronUp
                  className="ml-2 h-4 w-4 shrink-0 opacity-50"
                  color={`${showErrors && errors[name] && "hsl(20 89% 58%)"}`}
                />
              ) : (
                <ChevronDown
                  className="ml-2 h-4 w-4 shrink-0 opacity-50"
                  color={`${showErrors && errors[name] && "hsl(20 89% 58%)"}`}
                />
              )}
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 py-2 bg-white">
          <div className="max-h-60 overflow-y-auto">
            {showSelectAll && options.length > 1 && (
              <>
                <div
                  className="flex items-center space-x-3 px-3 py-2.5 hover:bg-gray-50 cursor-pointer"
                  onClick={handleSelectAll}
                >
                  <div
                    className={cn(
                      "flex items-center justify-center w-4 h-4 border-2 rounded-sm",
                      allSelected
                        ? "border-primary-800 bg-white"
                        : someSelected
                        ? "border-primary-800 bg-white"
                        : "border-neutral-600"
                    )}
                  >
                    {allSelected && (
                      <Check
                        className="h-3.5 w-3.5 text-primary-800"
                        strokeWidth={3}
                      />
                    )}
                    {someSelected && !allSelected && (
                      <Minus
                        className="h-3.5 w-3.5 text-primary-800"
                        strokeWidth={3}
                      />
                    )}
                  </div>
                  <span className="text-dark-gray-1000 text-sm">
                    Seleccionar todos
                  </span>
                </div>
                <div className="border-t border-gray-200 mx-3 my-2" />
              </>
            )}

            {hasEmptyValue && (
              <div
                className="flex items-center space-x-3 px-3 py-2 hover:bg-gray-50 cursor-pointer"
                onClick={() => handleClearAll()}
              >
                <div
                  className={cn(
                    "flex items-center justify-center w-4 h-4 border-2 rounded-sm",
                    selectedValues.length === 0
                      ? "border-primary-800 bg-white"
                      : "border-neutral-600"
                  )}
                >
                  {selectedValues.length === 0 && (
                    <Check className="h-3.5 w-3.5 text-primary-800" />
                  )}
                </div>
                <span className="text-dark-gray-1000 text-sm">
                  {placeholder}
                </span>
              </div>
            )}

            {options.map((option) => {
              const isSelected = selectedValues.includes(option.id);
              const isDisabled = option.disabled;

              return (
                <div
                  key={option.key ?? option.id}
                  className={cn(
                    "flex items-center space-x-3 px-3 py-2.5 ",
                    !isDisabled && "hover:bg-gray-50 cursor-pointer",
                    isDisabled && "cursor-not-allowed"
                  )}
                  onClick={() => !isDisabled && handleSelect(option.id)}
                >
                  <div
                    className={cn(
                      "flex items-center justify-center w-4 h-4 border-2 rounded-sm",
                      isDisabled
                        ? "border-neutral-600"
                        : isSelected
                        ? "border-primary-800 bg-white"
                        : "border-neutral-600"
                    )}
                  >
                    {isSelected && (
                      <Check
                        className={cn(
                          "h-3.5 w-3.5",
                          isDisabled ? "text-neutral-600" : "text-primary-800"
                        )}
                        strokeWidth={3}
                      />
                    )}
                  </div>
                  <div
                    className={cn(
                      "flex items-center text-sm",
                      isDisabled ? "text-neutral-600" : "text-dark-gray-1000"
                    )}
                  >
                    {dynamicLeftContent && dynamicLeftContent(option)}
                    {textFormatter ? textFormatter(option.text) : option.text}
                  </div>
                </div>
              );
            })}
          </div>
        </PopoverContent>
      </Popover>

      {showErrors && errors[name] && (
        <p className="text-error-500 text-sm font-medium mt-1">
          {errors[name]?.message as string}
        </p>
      )}
    </div>
  );
};
