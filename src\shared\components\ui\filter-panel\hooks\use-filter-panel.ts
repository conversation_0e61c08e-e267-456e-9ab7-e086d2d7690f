import { useRef } from "react";
import { useDrawerState } from "./use-drawer-state";
import { useFilterStores } from "./use-filter-stores";
import { useFilterActions } from "./use-filter-actions";
import { useFilterEffects } from "./use-filter-effects";

type UseFilterPanelProps = {
  onClearFilters?: () => void;
  onApplyFilters?: () => void;
};

export function useFilterPanel({
  onClearFilters,
  onApplyFilters,
}: UseFilterPanelProps = {}) {
  const drawerRef = useRef<HTMLDivElement>(null);
  const miniDrawerRef = useRef<HTMLDivElement>(null);

  const drawerState = useDrawerState();
  const filterStores = useFilterStores();

  const filterActions = useFilterActions({
    clearStaticPending: filterStores.clearStaticPending,
    clearDynamicPending: filterStores.clearDynamicPending,
    applyStaticFilters: filterStores.applyStaticFilters,
    applyPendingFilters: filterStores.applyPendingFilters,
    pendingCompany: filterStores.pendingCompany,
    appliedCompany: filterStores.appliedCompany,
    appliedDynamicFilters: filterStores.appliedDynamicFilters,
    ...drawerState,
    onClearFilters,
    onApplyFilters,
  });

  useFilterEffects({
    ...drawerState,
    drawerRef,
    miniDrawerRef,
    clearStaticPending: filterStores.clearStaticPending,
    clearDynamicPending: filterStores.clearDynamicPending,
    initializeStaticPending: filterStores.initializeStaticPending,
    initializePendingWithApplied: filterStores.initializePendingWithApplied,
  });

  return {
    drawerRef,
    miniDrawerRef,
    ...drawerState,
    activeFiltersCount: filterStores.activeFiltersCount,
    hasPendingChanges: filterStores.hasPendingChanges,
    pendingCompany: filterStores.pendingCompany,
    appliedCompany: filterStores.appliedCompany,
    appliedDynamicFilters: filterStores.appliedDynamicFilters,
    ...filterActions,
  };
}
