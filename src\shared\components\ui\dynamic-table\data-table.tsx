"use no memo";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  PaginationState,
  Table as ReactTable,
  RowSelectionState,
  Row,
  TableOptions,
} from "@tanstack/react-table";
import { useEffect, useMemo, useState } from "react";
import {
  MdArrowUpward,
  MdArrowDownward,
  MdChevronRight,
  MdChevronLeft,
  MdFirstPage,
  MdLastPage,
} from "react-icons/md";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./table";

import { Checkbox } from "../checkbox/checkbox";

export type TableData<TData> = TData & { id: string };

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TableData<TData>, TValue>[];
  data: TData[];
  pageSize?: number;
  emptyMessage?: string;
  enablePagination?: boolean;
  dataFilter?: (data: TData) => boolean;
  renderCustomFooter?: (
    table: ReactTable<TableData<TData>>,
    pagination: PaginationState
  ) => JSX.Element;
  styles?: Record<keyof typeof defaultStyles, string>;
  enableRowSelection?: boolean;
  customRowsRender?: (
    rows: Row<TableData<TData>>[],
    hasSelectColumn: boolean
  ) => JSX.Element;
  className?: string;
  tableConfig?: Partial<TableOptions<TData>>;
  onTableInit?: (table: ReactTable<TableData<TData>>) => void;
  rowClickHandler?: (row: Row<TableData<TData>>) => void;
}

const defaultStyles = {
  headerBackground: "bg-[#F5F1F9]",
  headerText: "text-[#6E4299]",
  rowHover: "hover:bg-[#F8F6FA]",
  selectedRow: "bg-[#F8F6FA]",
};

const pageButtonClass =
  "flex size-8 items-center justify-center rounded-full border-none hover:bg-slate-200 disabled:opacity-50";

const TableFooter = <TData,>({
  table,
  pagination,
}: {
  table: ReactTable<
    TData & {
      id: string;
    }
  >;
  pagination: PaginationState;
}) => {
  return (
    <div className="flex items-center space-x-2 py-4">
      <div className="flex-1">
        {pagination.pageIndex * pagination.pageSize}-
        {pagination.pageIndex * pagination.pageSize + pagination.pageSize} de{" "}
        {table.getRowCount()} resultados
      </div>
      <div className="flex flex-1 justify-end space-x-1">
        <button
          className={pageButtonClass}
          disabled={!table.getCanPreviousPage()}
          onClick={() => table.firstPage()}
        >
          <MdFirstPage className="size-5" />
        </button>
        <button
          className={pageButtonClass}
          disabled={!table.getCanPreviousPage()}
          onClick={() => table.previousPage()}
        >
          <MdChevronLeft className="size-5" />
        </button>
        <div className="flex items-center gap-1">
          <input
            className="w-6 rounded border border-slate-300 text-center [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
            type="number"
            value={pagination.pageIndex + 1}
          />{" "}
          de {table.getPageCount()}
        </div>
        <button
          className={pageButtonClass}
          disabled={!table.getCanNextPage()}
          onClick={() => table.nextPage()}
        >
          <MdChevronRight className="size-5" />
        </button>
        <button
          className={pageButtonClass}
          disabled={!table.getCanNextPage()}
          onClick={() => table.lastPage()}
        >
          <MdLastPage className="size-5" />
        </button>
      </div>
    </div>
  );
};

export function DataTable<TData, TValue>({
  columns,
  data,
  pageSize = 10,
  emptyMessage,
  enablePagination = true,
  renderCustomFooter,
  styles = defaultStyles,
  enableRowSelection = true,
  customRowsRender,
  tableConfig,
  className,
  onTableInit,
  rowClickHandler,
}: DataTableProps<TableData<TData>, TValue>) {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize,
  });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: enablePagination
      ? getPaginationRowModel()
      : undefined,
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onRowSelectionChange: setRowSelection,
    ...tableConfig,
    state: {
      sorting,
      columnFilters,
      pagination,
      rowSelection,
      ...tableConfig?.state,
    },
    getRowId: (row) => row.id,
    autoResetPageIndex: true,
    enableRowSelection,
  });

  const hasSelectColumn = useMemo(
    () => columns.some((column) => column.id === "select"),
    [columns]
  );

  useEffect(() => {
    onTableInit?.(table);
  }, [onTableInit, table]);

  return (
    <div className="w-[100vw] overflow-auto">
      <div className="rounded-lg border">
        <Table className={className}>
          <TableHeader className={"border-none"}>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {enableRowSelection && !hasSelectColumn && (
                  <TableHead className={"w-4"}>
                    <div className="flex items-center">
                      <Checkbox
                        checked={table.getIsAllRowsSelected()}
                        className="data-[state=checked]:bg-[#6E4299]"
                        partialChecked={table.getIsSomeRowsSelected()}
                        onCheckedChange={(checked) =>
                          table.toggleAllRowsSelected(checked ? true : false)
                        }
                      />
                    </div>
                  </TableHead>
                )}

                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      <div className="flex items-center gap-2">
                        {header.column.columnDef.enableSorting ? (
                          <>
                            <button
                              className="flex items-center gap-2"
                              onClick={() =>
                                header.column.toggleSorting(
                                  header.column.getIsSorted() === "asc"
                                )
                              }
                            >
                              {flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                              {header.column.getIsSorted() === "asc" ? (
                                <MdArrowUpward className="size-4" />
                              ) : (
                                <MdArrowDownward className="size-4" />
                              )}
                            </button>
                          </>
                        ) : (
                          <span>
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                          </span>
                        )}
                      </div>
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length > 0 ? (
              customRowsRender ? (
                customRowsRender(table.getRowModel().rows, hasSelectColumn)
              ) : (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.original.id}
                    className={
                      (row.getIsSelected() ? styles.selectedRow : "",
                      rowClickHandler && "cursor-pointer")
                    }
                    onClick={() => rowClickHandler?.(row)}
                  >
                    {enableRowSelection && !hasSelectColumn && (
                      <TableCell>
                        <div className="flex items-center">
                          <Checkbox
                            checked={row.getIsSelected()}
                            className="data-[state=checked]:bg-[#6E4299]"
                            onCheckedChange={row.getToggleSelectedHandler()}
                          />
                        </div>
                      </TableCell>
                    )}

                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={`${row.original.id}-${cell.id}`}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              )
            ) : (
              <TableRow>
                <TableCell
                  className="h-24 text-center"
                  colSpan={columns.length}
                >
                  {emptyMessage || "No hay resultados."}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {enablePagination &&
        (renderCustomFooter?.(table, pagination) || (
          <TableFooter pagination={pagination} table={table} />
        ))}
    </div>
  );
}
