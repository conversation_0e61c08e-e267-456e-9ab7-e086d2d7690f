import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cn } from "@styles/cn";
import * as React from "react";

type TabsTriggerProps = {
  size?: "sm" | "md" | "lg";
} & React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>;

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  TabsTriggerProps
>(({ className, children, size = "md", ...props }, ref) => {
  const sizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base",
  };

  return (
    <TabsPrimitive.Trigger
      ref={ref}
      className={cn(
        "inline-flex items-center justify-center whitespace-nowrap border-b-2 px-2 py-1 font-semibold transition-all focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 data-[state=active]:!border-b-primary-800 !text-neutral-600 data-[state=active]:!text-primary-800 cursor-pointer",
        sizeClasses[size],
        className
      )}
      {...props}
    >
      {children}
    </TabsPrimitive.Trigger>
  );
});

TabsTrigger.displayName = "TabsTrigger";

export { TabsTrigger };
