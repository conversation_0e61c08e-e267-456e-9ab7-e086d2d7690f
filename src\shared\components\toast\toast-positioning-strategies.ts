import type { PositioningStrategy, ToastPosition } from "./toast-container";

/**
 * Common positioning strategies for ToastContainer
 * These can be used as examples or directly imported for common use cases
 */

// Standard positions
export function createStaticPositioning(position: ToastPosition): PositioningStrategy {
  return () => position;
}

// Responsive positioning based on screen size
export function createResponsivePositioning(mobile: ToastPosition, desktop: ToastPosition, breakpoint = 768): PositioningStrategy {
  return () => {
    return window.innerWidth < breakpoint ? mobile : desktop;
  };
}

// Dynamic positioning based on any state/condition
export function createConditionalPositioning(condition: () => boolean, positionWhenTrue: ToastPosition, positionWhenFalse: ToastPosition): PositioningStrategy {
  return () => {
    return condition() ? positionWhenTrue : positionWhenFalse;
  };
}

// Positioning that avoids specific elements
export function createElementAwarePositioning(elementSelector: string, fallbackPosition: ToastPosition, offset = 16): PositioningStrategy {
  return () => {
    const element = document.querySelector(elementSelector);
    if (!element)
      return fallbackPosition;

    const rect = element.getBoundingClientRect();
    return {
      top: `${rect.bottom + offset}px`,
      right: `${window.innerWidth - rect.right}px`,
      zIndex: 9999,
    };
  };
}

// Common presets
export const POSITIONING_PRESETS = {
  // Standard positions
  topRight: createStaticPositioning({ top: "1rem", right: "1rem", zIndex: 9999 }),
  topLeft: createStaticPositioning({ top: "1rem", left: "1rem", zIndex: 9999 }),
  bottomRight: createStaticPositioning({ bottom: "1rem", right: "1rem", zIndex: 9999 }),
  bottomLeft: createStaticPositioning({ bottom: "1rem", left: "1rem", zIndex: 9999 }),
  topCenter: createStaticPositioning({ top: "1rem", left: "50%", zIndex: 9999 }),
  bottomCenter: createStaticPositioning({ bottom: "1rem", left: "50%", zIndex: 9999 }),

  // Below navbar (assuming 4rem nav height)
  belowNavRight: createStaticPositioning({ top: "4.5rem", right: "1rem", zIndex: 9999 }),
  belowNavLeft: createStaticPositioning({ top: "4.5rem", left: "1rem", zIndex: 9999 }),
  belowNavCenter: createStaticPositioning({ top: "4.5rem", left: "50%", zIndex: 9999 }),

  // Responsive positioning
  responsiveRight: createResponsivePositioning(
    { top: "1rem", right: "0.5rem", zIndex: 9999 }, // mobile
    { top: "1rem", right: "1rem", zIndex: 9999 }, // desktop
  ),
};
