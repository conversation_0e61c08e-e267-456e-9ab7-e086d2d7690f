{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/modules/*": ["src/modules/*"],
      "@/shared/*": ["src/shared/*"],
      "@/router/*": ["src/router/*"],
      "@/layout/*": ["src/layout/*"],
      // Legacy aliases (to be updated gradually)
      "@auth/*": ["src/modules/auth/*"],
      "@components/*": ["src/shared/components/ui/*"],
      "@constants/*": ["src/shared/constants/*"],
      "@forms/*": ["src/shared/forms/*"],
      "@hooks/*": ["src/shared/hooks/*"],
      "@layout/*": ["src/layout/*"],
      "@services/*": ["src/shared/services/*"],
      "@providers/*": ["src/shared/providers/*"],
      "@stores/*": ["src/shared/stores/*"],
      "@styles/*": ["src/styles/*"]
    },
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "Bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  "include": ["src"]
}
