import {
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "lucide-react";
import { DayButton, DayPicker, getDefaultClassNames } from "react-day-picker";
import { es } from "date-fns/locale";

import { Button } from "@components/button";
import { buttonVariants } from "@components/button/button.styles";
import { cn } from "@styles/cn";
import { useEffect, useMemo, useRef } from "react";

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  captionLayout = "label",
  buttonVariant = "link",
  formatters,
  components,
  locale = es,
  disableFutureDates = false,
  ...props
}: React.ComponentProps<typeof DayPicker> & {
  buttonVariant?: React.ComponentProps<typeof Button>["variant"];
  disableFutureDates?: boolean;
}) {
  const defaultClassNames = getDefaultClassNames();

  const disabledDays = useMemo(() => {
    if (!disableFutureDates) return props.disabled;
    const futureDatesDisabled = { after: new Date() };
    if (!props.disabled) return futureDatesDisabled;
    return Array.isArray(props.disabled)
      ? [futureDatesDisabled, ...props.disabled]
      : [futureDatesDisabled, props.disabled];
  }, [disableFutureDates, props.disabled]);

  return (
    <DayPicker
      locale={locale}
      showOutsideDays={showOutsideDays}
      disabled={disabledDays}
      className={cn(
        "group/calendar p-4 [--cell-size:--spacing(10)] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent",
        String.raw`rtl:**:[.rdp-button\_next>svg]:rotate-180`,
        String.raw`rtl:**:[.rdp-button\_previous>svg]:rotate-180`,
        className
      )}
      captionLayout={captionLayout}
      formatters={{
        formatMonthDropdown: (date) =>
          date.toLocaleString("es", { month: "long" }),
        formatCaption: (date) => {
          const month = date.toLocaleString("es", { month: "long" });
          const year = date.getFullYear();
          // Capitalize first letter of month
          const capitalizedMonth =
            month.charAt(0).toUpperCase() + month.slice(1);
          return `${capitalizedMonth} ${year}`;
        },
        ...formatters,
      }}
      classNames={{
        root: cn("w-fit", defaultClassNames.root),
        months: cn(
          "flex gap-4 flex-col md:flex-row relative",
          defaultClassNames.months
        ),
        month: cn("flex flex-col w-full gap-4", defaultClassNames.month),
        nav: cn(
          "flex items-center gap-1 w-full absolute top-1 inset-x-0 justify-between z-10 px-6",
          defaultClassNames.nav
        ),
        button_previous: cn(
          buttonVariants({ variant: buttonVariant }),
          "size-(--cell-size) aria-disabled:opacity-50 p-0 select-none text-primary-800 hover:bg-transparent hover:border-primary-800 hover:border-2 border-2 border-transparent rounded-full transition-all duration-200",
          defaultClassNames.button_previous
        ),
        button_next: cn(
          buttonVariants({ variant: buttonVariant }),
          "size-(--cell-size) aria-disabled:opacity-50 p-0 select-none text-primary-800 hover:bg-transparent hover:border-primary-800 hover:border-2 border-2 border-transparent rounded-full transition-all duration-200",
          defaultClassNames.button_next
        ),
        month_caption: cn(
          "flex items-center justify-center h-(--cell-size) w-full px-(--cell-size) font-medium text-lg text-primary-800",
          defaultClassNames.month_caption
        ),
        dropdowns: cn(
          "w-full flex items-center text-lg font-medium justify-center h-(--cell-size) gap-1.5 text-primary-800",
          defaultClassNames.dropdowns
        ),
        dropdown_root: cn(
          "relative has-focus:border-ring border border-input has-focus:ring-ring/50 has-focus:ring-[3px] rounded-md",
          defaultClassNames.dropdown_root
        ),
        dropdown: cn("absolute inset-0 opacity-0", defaultClassNames.dropdown),
        caption_label: cn(
          "select-none font-medium text-primary-800",
          captionLayout === "label"
            ? "text-lg"
            : "rounded-md pl-2 pr-1 flex items-center gap-1 text-lg h-10 [&>svg]:text-muted-foreground [&>svg]:size-3.5",
          defaultClassNames.caption_label
        ),
        table: "w-full border-collapse mt-6",
        weekdays: cn("flex mb-2", defaultClassNames.weekdays),
        weekday: cn(
          "text-gray-500 rounded-md flex-1 font-medium text-sm select-none h-10 flex items-center justify-center uppercase",
          defaultClassNames.weekday
        ),
        week: cn("flex w-full", defaultClassNames.week),
        week_number_header: cn(
          "select-none w-(--cell-size)",
          defaultClassNames.week_number_header
        ),
        week_number: cn(
          "text-[0.8rem] select-none text-muted-foreground",
          defaultClassNames.week_number
        ),
        day: cn(
          "relative w-full h-full p-0 text-center group/day aspect-square select-none mb-2",
          defaultClassNames.day
        ),
        range_start: cn(
          "rounded-full bg-primary-800",
          defaultClassNames.range_start
        ),
        range_middle: cn("rounded-none", defaultClassNames.range_middle),
        range_end: cn(
          "rounded-full bg-primary-800",
          defaultClassNames.range_end
        ),
        today: cn(
          "bg-primary-800/20 text-gray-900 font-medium rounded-full",
          defaultClassNames.today
        ),
        outside: cn(
          "text-gray-400 opacity-50 aria-selected:text-gray-400",
          defaultClassNames.outside
        ),
        disabled: cn("text-gray-300", defaultClassNames.disabled),
        hidden: cn("invisible", defaultClassNames.hidden),
        ...classNames,
      }}
      components={{
        Root: ({ className, rootRef, ...props }) => {
          return (
            <div
              data-slot="calendar"
              ref={rootRef}
              className={cn(className, "bg-zinc-50")}
              {...props}
            />
          );
        },
        Chevron: ({ className, orientation, ...props }) => {
          if (orientation === "left") {
            return (
              <ChevronLeftIcon className={cn("size-6", className)} {...props} />
            );
          }

          if (orientation === "right") {
            return (
              <ChevronRightIcon
                className={cn("size-6", className)}
                {...props}
              />
            );
          }

          return (
            <ChevronDownIcon className={cn("size-6", className)} {...props} />
          );
        },
        DayButton: CalendarDayButton,
        WeekNumber: ({ children, ...props }) => {
          return (
            <td {...props}>
              <div className="flex size-(--cell-size) items-center justify-center text-center">
                {children}
              </div>
            </td>
          );
        },
        ...components,
      }}
      {...props}
    />
  );
}

function CalendarDayButton({
  className,
  day,
  modifiers,
  ...props
}: React.ComponentProps<typeof DayButton>) {
  const defaultClassNames = getDefaultClassNames();

  const ref = useRef<HTMLButtonElement>(null);
  useEffect(() => {
    if (modifiers.focused) ref.current?.focus();
  }, [modifiers.focused]);

  return (
    <Button
      ref={ref}
      variant="link"
      size="sm"
      data-day={day.date.toLocaleDateString()}
      data-selected-single={
        modifiers.selected &&
        !modifiers.range_start &&
        !modifiers.range_end &&
        !modifiers.range_middle
      }
      data-range-start={modifiers.range_start}
      data-range-end={modifiers.range_end}
      data-range-middle={modifiers.range_middle}
      className={cn(
        "data-[selected-single=true]:bg-primary-800 data-[selected-single=true]:text-white data-[selected-single=true]:border-2 data-[selected-single=true]:border-primary-800 data-[selected-single=true]:rounded-full data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary-800 data-[range-start=true]:text-white data-[range-start=true]:rounded-full data-[range-end=true]:bg-primary-800 data-[range-end=true]:text-white data-[range-end=true]:rounded-full group-data-[focused=true]/day:border-primary-800 group-data-[focused=true]/day:ring-ring/50 hover:border-primary-800 flex aspect-square size-10 w-10 h-10 min-w-10 min-h-10 flex-col gap-1 leading-none font-medium group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] rounded-full text-gray-700 border-transparent border-2 transition-all duration-200",
        defaultClassNames.day,
        className
      )}
      {...props}
    />
  );
}

export { Calendar, CalendarDayButton };
