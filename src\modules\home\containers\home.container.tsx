import { HomeView } from "../views/home.view";
import { useSearchParams } from "react-router";
import { useAppliedDateRange } from "@/shared/components/ui/filter-panel/stores/filter-store";
import { createTimeParams } from "@/shared/components/ui/filter-panel/utils/date-helpers";
import { useState, useCallback, useMemo } from "react";
import { useAuthToken } from "@/modules/auth";
import { columnsF14Reports } from "../components/columns-f14-reports";
import { columnsF34Reports } from "../components/columns-f34-reports";
import { useF14ReportsQuery, useF34ReportsQuery } from "../hooks/use-reports-query";
import { F14ReportTableData, F34ReportTableData } from "@/shared/services/mappers/reports-mapper";

export const HomeContainer = () => {
  const appliedDateRange = useAppliedDateRange();
  const [searchParams, setSearchParams] = useSearchParams();
  const { token } = useAuthToken();

  // Convert date range to YYYY-MM-DD format for API
  const { startTime, endTime } = createTimeParams(appliedDateRange);
  
  // Format dates for Mercury API (YYYY-MM-DD)
  const formatDateForAPI = (dateString: string | null): string | null => {
    if (!dateString) return null;
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    } catch {
      return null;
    }
  };

  const fromDate = formatDateForAPI(startTime);
  const toDate = formatDateForAPI(endTime);

  // Search states for each tab
  const [searchF14Reports, setSearchF14Reports] = useState<string>("");
  const [searchF34Reports, setSearchF34Reports] = useState<string>("");

  const handleTabChange = (value: string) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("tab", value);
    // Clear search when switching tabs
    setSearchParams(newParams);
    setSearchF14Reports("");
    setSearchF34Reports("");
  };

  // Fetch F14 reports
  const { data: f14ReportsData, isLoading: isLoadingF14Reports } = useF14ReportsQuery(
    fromDate,
    toDate,
    token || ""
  );

  // Fetch F34 reports
  const { data: f34ReportsData, isLoading: isLoadingF34Reports } = useF34ReportsQuery(
    fromDate,
    toDate,
    token || ""
  );

  // Filter data based on search
  const filteredF14Data = useMemo((): F14ReportTableData[] => {
    if (!f14ReportsData || !searchF14Reports) return f14ReportsData || [];
    
    const searchLower = searchF14Reports.toLowerCase();
    return f14ReportsData.filter((item: F14ReportTableData) =>
      item.licensePlate.toLowerCase().includes(searchLower) ||
      item.destinationType.toLowerCase().includes(searchLower) ||
      item.destinationCode.toLowerCase().includes(searchLower) ||
      item.recyclingArea.toLowerCase().includes(searchLower) ||
      item.extendedRouteCode.toLowerCase().includes(searchLower) ||
      item.nuap.toString().includes(searchLower) ||
      item.serviceTicketId.toString().includes(searchLower)
    );
  }, [f14ReportsData, searchF14Reports]);

  const filteredF34Data = useMemo((): F34ReportTableData[] => {
    if (!f34ReportsData || !searchF34Reports) return f34ReportsData || [];
    
    const searchLower = searchF34Reports.toLowerCase();
    return f34ReportsData.filter((item: F34ReportTableData) =>
      item.licensePlate.toLowerCase().includes(searchLower) ||
      item.companyName.toLowerCase().includes(searchLower) ||
      item.nit.toLowerCase().includes(searchLower) ||
      item.daneCode.toLowerCase().includes(searchLower) ||
      item.nusd.toLowerCase().includes(searchLower) ||
      item.originType.toLowerCase().includes(searchLower) ||
      item.serviceTicketId.toString().includes(searchLower)
    );
  }, [f34ReportsData, searchF34Reports]);

  const handleSearchF14Change = useCallback((value: string) => {
    setSearchF14Reports(value);
  }, []);

  const handleSearchF34Change = useCallback((value: string) => {
    setSearchF34Reports(value);
  }, []);

  return (
    <HomeView
      tableColumnsF14Reports={columnsF14Reports}
      tableDataF14Reports={filteredF14Data}
      isLoadingF14Reports={isLoadingF14Reports}
      tableColumnsF34Reports={columnsF34Reports}
      tableDataF34Reports={filteredF34Data}
      isLoadingF34Reports={isLoadingF34Reports}
      searchF14Reports={searchF14Reports}
      searchF34Reports={searchF34Reports}
      onSearchF14ReportsChange={handleSearchF14Change}
      onSearchF34ReportsChange={handleSearchF34Change}
      onTabChange={handleTabChange}
      hasDateRange={!!fromDate && !!toDate}
    />
  );
};
