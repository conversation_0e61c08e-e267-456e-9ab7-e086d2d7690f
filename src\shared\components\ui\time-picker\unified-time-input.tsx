import { cn } from "@styles/cn";
import { TimePicker } from "./time-picker";
import { useFormContext } from "react-hook-form";

interface BaseTimeInputProps {
  label: string;
  className?: string;
  required?: boolean;
}

interface FormTimeInputProps extends BaseTimeInputProps {
  name: string;
  value?: never;
  onChange?: never;
}

interface ControlledTimeInputProps extends BaseTimeInputProps {
  name?: never;
  value: string;
  onChange: (value: string) => void;
}

type UnifiedTimeInputProps = FormTimeInputProps | ControlledTimeInputProps;

// Form version component
const FormTimeInput = ({ label, name, className, required = false }: FormTimeInputProps) => {
  const {
    setValue,
    watch,
    clearErrors,
    formState: { errors },
  } = useFormContext();

  const value = watch(name);

  const handleDateChange = (date: Date | undefined) => {
    setValue(name, date);
    clearErrors(name);
  };

  return (
    <div className={cn("time-input-wrapper", className)}>
      <TimePicker
        date={value}
        setDate={handleDateChange}
        title={label}
        showLabels={false}
        className="[&>input]:w-12 [&>input]:h-10"
        required={required}
      />
      {errors[name] && (
        <p className="text-error-500 text-sm font-medium mt-1">
          {errors[name]?.message as string}
        </p>
      )}
    </div>
  );
};

// Controlled version component
const ControlledTimeInput = ({
  label,
  value,
  onChange,
  className,
  required = false,
}: ControlledTimeInputProps) => {
  const stringToDate = (timeString: string): Date => {
    if (!timeString) {
      const now = new Date();
      now.setHours(0, 0, 0, 0);
      return now;
    }
    const [hours, minutes] = timeString.split(":").map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    return date;
  };

  const dateToString = (date: Date): string => {
    if (!date || isNaN(date.getTime())) {
      return "";
    }
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
  };

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      const timeString = dateToString(date);
      onChange(timeString);
    }
  };

  return (
    <div className={cn("time-input-wrapper", className)}>
      <TimePicker
        date={stringToDate(value)}
        setDate={handleDateChange}
        title={label}
        showLabels={false}
        className="[&>input]:w-12 [&>input]:h-10"
        required={required}
      />
    </div>
  );
};

export const UnifiedTimeInput = (props: UnifiedTimeInputProps) => {
  if ("name" in props) {
    return <FormTimeInput {...(props as FormTimeInputProps)} />;
  } else {
    return <ControlledTimeInput {...(props as ControlledTimeInputProps)} />;
  }
};
