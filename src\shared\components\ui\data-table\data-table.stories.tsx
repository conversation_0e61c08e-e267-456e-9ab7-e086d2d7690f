import type { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";
import { fn } from "@storybook/test";
import type { ColumnDef, Row } from "@tanstack/react-table";
import { flexRender } from "@tanstack/react-table";
import { Button } from "@components/button";
import { TableCell, TableRow } from "@components/table";
import { DataTable, type TableData } from "./data-table";

const meta = {
  title: "Molecules/DataTable",
  component: DataTable,
  tags: ["autodocs", "components"],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Data table component that supports sorting, filtering, pagination, and row selection.",
      },
    },
    actions: {
      argTypesRegex: "^on[A-Z].*",
    },
  },
  argTypes: {
    pageSize: {
      description: "The page size of the table",
      control: "number",
      table: {
        defaultValue: { summary: "10" },
        type: { summary: "number" },
      },
    },
    pagination: {
      description:
        "Whether to enable pagination, if false all data will be displayed",
      control: "boolean",
      table: {
        defaultValue: { summary: "true" },
        type: { summary: "boolean" },
      },
    },
    enableRowSelection: {
      description: "Whether to enable row selection",
      control: "boolean",
      table: {
        defaultValue: { summary: "true" },
        type: { summary: "boolean" },
      },
    },
    emptyMessage: {
      description: "The message to display when the table is empty",
      control: "text",
      table: {
        defaultValue: { summary: "No data" },
        type: { summary: "string" },
      },
    },
    styles: {
      description: "The styles to apply to the table",
      control: "object",
      table: {
        type: { summary: "object" },
      },
    },
    renderCustomFooter: {
      description: "A custom footer component to render",
      control: "object",
      table: {
        type: { summary: "object" },
      },
    },
    customRowsRender: {
      description: "A custom row renderer",
      control: "object",
      table: {
        type: { summary: "object" },
      },
    },
    tableConfig: {
      description: "The configuration object for the table",
      control: "object",
      table: {
        type: { summary: "object" },
      },
    },
    className: {
      description: "Additional CSS classes to apply to the table",
      control: "text",
      table: {
        type: { summary: "string" },
      },
    },
    data: {
      description:
        "The data to display in the table, must be an array of using the defined columns.",
      control: "object",
      table: {
        type: { summary: "array" },
      },
    },
    columns: {
      description:
        "The columns to display in the table, each key must match the key in the data object",
      control: "object",
      table: {
        type: {
          summary: JSON.stringify({
            id: "string",
            accessorKey: "string",
            header: "string",
            enableSorting: "boolean",
          }),
        },
      },
    },
  },
} satisfies Meta<typeof DataTable>;

export default meta;

type Person = {
  id: string;
  firstName: string;
  lastName: string;
  age: number;
  visits: number;
  country: string;
};

const data: Person[] = [
  {
    id: "d64903ff-4dfd-4130-b1ba-e5927ac50647",
    firstName: "Ettie",
    lastName: "Dickens",
    age: 28,
    visits: 592,
    country: "Mexico",
  },
  {
    id: "77e5080d-7dfe-43d4-a97d-bf017ce9d3f1",
    firstName: "Elsa",
    lastName: "Lehner",
    age: 9,
    visits: 311,
    country: "Argentina",
  },
  {
    id: "fe179447-deca-46c5-bcdb-483cfbed6007",
    firstName: "Issac",
    lastName: "Cummings",
    age: 4,
    visits: 182,
    country: "Argentina",
  },
  {
    id: "47dfbb4f-2656-4e50-9389-9782dff7e737",
    firstName: "Ettie",
    lastName: "Stanton",
    age: 14,
    visits: 943,
    country: "Mexico",
  },
  {
    id: "6a1c2599-4e7f-4533-b069-769a4e0f0b02",
    firstName: "Nelle",
    lastName: "Frami",
    age: 24,
    visits: 326,
    country: "Argentina",
  },
  {
    id: "fdb78ead-1c8d-482c-ade3-6827c63a8f20",
    firstName: "Eleanore",
    lastName: "Torp",
    age: 12,
    visits: 137,
    country: "Spain",
  },
  {
    id: "790477e0-9768-4bdf-b55a-015720c0efed",
    firstName: "Chasity",
    lastName: "Bogan",
    age: 29,
    visits: 268,
    country: "Chile",
  },
  {
    id: "c9eaf3f3-345a-4cd6-b05b-c821597f955b",
    firstName: "Brant",
    lastName: "Donnelly",
    age: 28,
    visits: 551,
    country: "Chile",
  },
  {
    id: "0e2b8f87-0be8-4025-a4e2-bede626e0136",
    firstName: "Troy",
    lastName: "Emmerich",
    age: 27,
    visits: 662,
    country: "United States",
  },
  {
    id: "19cf1f50-98df-47a5-834c-26e1f2167d9f",
    firstName: "Wilfredo",
    lastName: "Wilkinson",
    age: 17,
    visits: 30,
    country: "United States",
  },
  {
    id: "719e8283-c2b4-4b0d-98c4-554485987842",
    firstName: "Ulices",
    lastName: "Little",
    age: 18,
    visits: 652,
    country: "United States",
  },
  {
    id: "192f01d8-e4a1-4758-a9a9-c2f768450b54",
    firstName: "Alexa",
    lastName: "Ankunding",
    age: 9,
    visits: 139,
    country: "Mexico",
  },
  {
    id: "7abd4496-8234-4e68-bfdf-7c0773b020ac",
    firstName: "Jana",
    lastName: "Kuhic",
    age: 36,
    visits: 773,
    country: "Canada",
  },
  {
    id: "102b2103-652a-4de1-8168-79304915bd40",
    firstName: "Marcelina",
    lastName: "Williamson",
    age: 14,
    visits: 438,
    country: "Canada",
  },
  {
    id: "5673aaf4-250e-4810-abc5-59f54d23d961",
    firstName: "Lyla",
    lastName: "Daugherty",
    age: 35,
    visits: 992,
    country: "Canada",
  },
  {
    id: "f7a4e16f-775b-4acc-9da4-9106a146b3cc",
    firstName: "Derick",
    lastName: "Lang",
    age: 13,
    visits: 996,
    country: "Portugal",
  },
  {
    id: "5fa30563-56c5-4f02-986b-fba4be7f895e",
    firstName: "Karlee",
    lastName: "Hyatt",
    age: 17,
    visits: 818,
    country: "Japan",
  },
];

const columns: ColumnDef<Person>[] = [
  {
    id: "firstName",
    accessorKey: "firstName",
    header: "Nombre",
  },
  {
    id: "lastName",
    accessorKey: "lastName",
    header: "Apellido",
  },
  {
    id: "age",
    accessorKey: "age",
    header: "Edad",
  },
  {
    id: "visits",
    accessorKey: "visits",
    header: "Visitas",
  },
  {
    id: "country",
    accessorKey: "country",
    header: "País",
  },
];

type Story = StoryObj<typeof DataTable<Person, string>>;

export const Default: Story = {
  args: {
    columns,
    data,
    onTableInit: fn(),
    pageSize: 5,
  },
};

export const NoPagination: Story = {
  args: {
    columns,
    data: data.slice(0, 5),
    onTableInit: fn(),
    pagination: {
      type: "none",
    },
  },
};

export const ColumnSorting: Story = {
  args: {
    columns: columns.map((col) => ({
      ...col,
      enableSorting: col.id !== "visits" && col.id !== "age",
    })),
    data,
    pageSize: 5,
    onTableInit: fn(),
  },
};

export const RowSelection: Story = {
  args: {
    columns,
    data,
    pageSize: 5,
    onTableInit: fn(),
    enableRowSelection: true,
  },
};

export const CustomFooter: Story = {
  args: {
    columns,
    data,
    pageSize: 5,
    onTableInit: fn(),
    renderCustomFooter: (_, pagination) => (
      <div className="flex flex-col items-center space-x-2 p-2 text-sm font-bold text-blue-700">
        <div>
          Custom Table Footer with in page
          {pagination.pageIndex + 1} out of
          {pagination.pageSize}, fully customizable.
        </div>
        <div>You can easily access the table data and pagination info</div>
      </div>
    ),
  },
};

export const CustomRowRender: Story = {
  args: {
    columns,
    data,
    pageSize: 5,
    onTableInit: fn(),
    customRowsRender: (data: Row<TableData<Person>>[]) => {
      return (
        <>
          {data.map((row) => (
            <TableRow key={row.original.id}>
              {row.getVisibleCells().map((cell) => (
                <TableCell key={`${row.original.id}-${cell.id}`}>
                  <Button
                    onClick={() => alert(`You clicked on ${cell.getValue()}`)}
                    variant="secondary"
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </Button>
                </TableCell>
              ))}
            </TableRow>
          ))}
        </>
      );
    },
  },
};

export const CustomStyles: Story = {
  args: {
    columns,
    data,
    pageSize: 5,
    enableRowSelection: true,
    onTableInit: fn(),
    styles: {
      headerText: "text-gray-800",
      headerBackground: "bg-gray-200",
      rowHover: "hover:bg-red-300",
      selectedRow: "bg-blue-300",
    },
  },
};

export const EmptyMessage: Story = {
  args: {
    columns,
    data: [],
    pageSize: 5,
    onTableInit: fn(),
    emptyMessage: "No hay datos",
  },
};

export const RowClick: Story = {
  args: {
    columns,
    data,
    pageSize: 5,
    onTableInit: fn(),
    rowClickHandler: (row) =>
      alert(`You clicked on row with name ${row.original.firstName}`),
  },
};
