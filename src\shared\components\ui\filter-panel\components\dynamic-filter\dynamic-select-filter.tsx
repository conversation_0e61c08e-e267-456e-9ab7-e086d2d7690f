import { useState } from "react";
import { DropdownType } from "@components/dropdown";
import { RadioGroup, VirtualizedRadioGroup } from "@components/radio-group";
import { Input, SearchIcon } from "@urbetrack/urbix";

interface DynamicSelectFilterProps {
  value: string;
  onValueChange: (value: string) => void;
  options: DropdownType[];
  placeholder: string;
  searchable: boolean;
  moveSelectedToTop: boolean;
}

export const DynamicSelectFilter = ({
  value,
  onValueChange,
  options,
  placeholder,
  searchable,
  moveSelectedToTop,
}: DynamicSelectFilterProps) => {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredOptions = (() => {
    let filteredOpts = options;

    if (searchQuery.trim()) {
      filteredOpts = options.filter((option) =>
        option.text.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (moveSelectedToTop && value) {
      const selectedOption = filteredOpts.find(
        (opt) => opt.id.toString() === value
      );
      const otherOptions = filteredOpts.filter(
        (opt) => opt.id.toString() !== value
      );

      if (selectedOption) {
        return [selectedOption, ...otherOptions];
      }
    }

    return filteredOpts;
  })();

  const virtualizedItems = filteredOptions.map((option) => ({
    value: option.id.toString(),
    label: option.text,
  }));

  return (
    <div className="h-full flex flex-col">
      {searchable && (
        <div className="mb-3 flex-shrink-0">
          <Input
            placeholder={placeholder}
            iconRight={<SearchIcon />}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              setSearchQuery(e.target.value);
            }}
            value={searchQuery}
          />
        </div>
      )}

      {virtualizedItems.length === 0 ? (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          No se encontraron opciones
        </div>
      ) : virtualizedItems.length > 15 ? (
        <VirtualizedRadioGroup
          value={value}
          onValueChange={onValueChange}
          items={virtualizedItems}
          containerHeight={searchable ? "calc(100% - 60px)" : "100%"}
          className="flex-1"
        />
      ) : (
        <div className="flex-1 overflow-auto">
          <RadioGroup value={value} onValueChange={onValueChange}>
            {virtualizedItems.map((item) => (
              <RadioGroup.Item
                key={item.value}
                value={item.value}
                label={item.label}
                variant="hover"
              />
            ))}
          </RadioGroup>
        </div>
      )}
    </div>
  );
};

DynamicSelectFilter.displayName = "DynamicSelectFilter";
