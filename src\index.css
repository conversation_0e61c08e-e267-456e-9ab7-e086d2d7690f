@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap");
@import "tailwindcss";
@import "tw-animate-css";

@theme {
  --color-violeta: hsl(272 51% 42%);
  --color-gcaba: hsl(54 93% 49%);
  --color-telefonica: hsl(207 71% 51%);
  --color-red: hsl(20 89% 58%);
  --color-pp03: hsl(143 70% 42%);
  --color-dark-gray-1000: hsl(var(--dark-gray-1000));

  --color-primary-1000: hsl(var(--primary-1000));
  --color-primary-800: hsl(var(--primary-800));
  --color-primary-700: hsl(var(--primary-700));
  --color-primary-600: hsl(var(--primary-600));
  --color-primary-500: hsl(var(--primary-500));
  --color-primary-400: hsl(var(--primary-400));
  --color-primary-300: hsl(var(--primary-300));
  --color-primary-200: hsl(var(--primary-200));
  --color-primary-100: hsl(var(--primary-100));
  --color-primary-50: hsl(var(--primary-50));

  --color-secondary-1000: hsl(var(--secondary-1000));
  --color-secondary-800: hsl(var(--secondary-800));
  --color-secondary-700: hsl(var(--secondary-700));
  --color-secondary-600: hsl(var(--secondary-600));
  --color-secondary-500: hsl(var(--secondary-500));
  --color-secondary-400: hsl(var(--secondary-400));
  --color-secondary-300: hsl(var(--secondary-300));
  --color-secondary-200: hsl(var(--secondary-200));
  --color-secondary-100: hsl(var(--secondary-100));
  --color-secondary-50: hsl(var(--secondary-50));

  --color-gray-1000: hsl(var(--gray-1000));
  --color-gray-800: hsl(var(--gray-800));
  --color-gray-600: hsl(var(--gray-600));
  --color-gray-400: hsl(var(--gray-400));
  --color-gray-200: hsl(var(--gray-200));
  --color-gray-100: hsl(var(--gray-100));
  --color-gray-50: hsl(var(--gray-50));

  --color-neutral-black: hsl(var(--neutral-black));
  --color-neutral-1000: hsl(var(--neutral-1000));
  --color-neutral-800: hsl(var(--neutral-800));
  --color-neutral-700: hsl(var(--neutral-700));
  --color-neutral-600: hsl(var(--neutral-600));
  --color-neutral-500: hsl(var(--neutral-500));
  --color-neutral-400: hsl(var(--neutral-400));
  --color-neutral-300: hsl(var(--neutral-300));
  --color-neutral-200: hsl(var(--neutral-200));
  --color-neutral-100: hsl(var(--neutral-100));
  --color-neutral-50: hsl(var(--neutral-50));
  --color-neutral-white: hsl(var(--neutral-white));

  --color-information-1000: hsl(var(--information-1000));
  --color-information-800: hsl(var(--information-800));
  --color-information-500: hsl(var(--information-500));
  --color-information-400: hsl(var(--information-400));
  --color-information-300: hsl(var(--information-300));
  --color-information-200: hsl(var(--information-200));

  --color-warning-1000: hsl(var(--warning-1000));
  --color-warning-800: hsl(var(--warning-800));
  --color-warning-500: hsl(var(--warning-500));
  --color-warning-400: hsl(var(--warning-400));
  --color-warning-300: hsl(var(--warning-300));
  --color-warning-200: hsl(var(--warning-200));

  --color-success-1000: hsl(var(--success-1000));
  --color-success-800: hsl(var(--success-800));
  --color-success-500: hsl(var(--success-500));
  --color-success-400: hsl(var(--success-400));
  --color-success-300: hsl(var(--success-300));
  --color-success-200: hsl(var(--success-200));

  --color-error-1000: hsl(var(--error-1000));
  --color-error-800: hsl(var(--error-800));
  --color-error-500: hsl(var(--error-500));
  --color-error-400: hsl(var(--error-400));
  --color-error-300: hsl(var(--error-300));
  --color-error-200: hsl(var(--error-200));

  --animate-fadeinleft: fade-in-left 0.35s ease-out 0s;
  --animate-fadeinright: fade-in-right 0.35s ease-out 0s;
  --animate-fadeoutright: fade-out-right 0.35s ease-out 0s;
  --animate-fadeoutleft: fade-out-left 0.35s ease-out 0s;
}

@keyframes fade-in-left {
  0% {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fade-in-right {
  0% {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fade-out-right {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes fade-out-left {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }
}

@layer base {
  :root {
    --toastify-font-family: "Montserrat", sans-serif;
    --toastify-color-success: #14a96b;
    --toastify-color-error: #f44040;
    --toastify-color-warning: #f37820;
    --toastify-color-info: #1e4f97;

    --gcaba: 54 93% 49%;
    --telefonica: 207 71% 51%;
    --red: 20 89% 58%;
    --pp03: 143 70% 42%;
    --violeta: 272 51% 42%;

    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --dark-gray-1000: 236 10% 29%;

    --primary-1000: 272 51% 21%;
    --primary-800: 270 40% 43%;
    --primary-700: 271 41% 48%;
    --primary-600: 269 48% 56%;
    --primary-500: 264 46% 64%;
    --primary-400: 267 56% 75%;
    --primary-300: 267 52% 84%;
    --primary-200: 265 57% 92%;
    --primary-100: 268 89% 96%;
    --primary-50: 270 60% 98%;

    --secondary-1000: 25 77% 48%;
    --secondary-800: 25 90% 54%;
    --secondary-700: 28 91% 61%;
    --secondary-600: 28 91% 61%;
    --secondary-500: 31 91% 72%;
    --secondary-400: 31 91% 72%;
    --secondary-300: 33 100% 89%;
    --secondary-200: 33 100% 89%;
    --secondary-100: 33 100% 96%;
    --secondary-50: 33 100% 96%;

    --gray-1000: 236 10% 29%;
    --gray-800: 200 10% 39%;
    --gray-600: 197 13% 56%;
    --gray-400: 195 16% 75%;
    --gray-200: 200 14% 92%;
    --gray-100: 200 27% 96%;
    --gray-50: 210 20% 98%;

    --neutral-black: 0 0% 0%;
    --neutral-1000: 236 10% 29%;
    --neutral-800: 200 10% 39%;
    --neutral-700: 199 13% 48%;
    --neutral-600: 197 13% 56%;
    --neutral-500: 198 13% 64%;
    --neutral-400: 195 16% 75%;
    --neutral-300: 195 23% 86%;
    --neutral-200: 195 25% 94%;
    --neutral-100: 200 27% 96%;
    --neutral-50: 210 20% 98%;
    --neutral-white: 0 0% 100%;

    --information-1000: 207 74% 21%;
    --information-800: 221 45% 48%;
    --information-500: 205 100% 50%;
    --information-400: 191 100% 85%;
    --information-300: 191 100% 91%;
    --information-200: 190 100% 96%;

    --warning-1000: 28 100% 45%;
    --warning-800: 37 98% 45%;
    --warning-500: 35 100% 55%;
    --warning-400: 41 100% 77%;
    --warning-300: 40 96% 90%;
    --warning-200: 44 100% 97%;

    --success-1000: 156 100% 25%;
    --success-800: 155 99% 29%;
    --success-500: 153 98% 32%;
    --success-400: 151 56% 74%;
    --success-300: 153 46% 54%;
    --success-200: 153 46% 69%;

    --error-1000: 0 100% 50%;
    --error-800: 0 66% 47%;
    --error-500: 0 75% 54%;
    --error-400: 0 96% 82%;
    --error-300: 0 61% 91%;
    --error-200: 0 100% 98%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --primary-1000: 272 51% 21%;
    --primary-800: 270 40% 43%;
    --primary-600: 269 48% 56%;
    --primary-400: 267 56% 75%;
    --primary-200: 265 57% 92%;
    --primary-50: 270 60% 98%;

    --secondary-1000: 25 77% 48%;
    --secondary-800: 25 90% 54%;
    --secondary-600: 28 91% 61%;
    --secondary-400: 31 91% 72%;
    --secondary-200: 33 100% 89%;
    --secondary-50: 33 100% 96%;

    --gray-1000: 236 10% 29%;
    --gray-800: 200 10% 39%;
    --gray-600: 197 13% 56%;
    --gray-400: 195 16% 75%;
    --gray-200: 200 14% 92%;
    --gray-100: 200 27% 96%;
    --gray-50: 210 20% 98%;

    --neutral-black: 0 0% 0%;
    --neutral-1000: 236 10% 29%;
    --neutral-800: 200 10% 39%;
    --neutral-700: 199 13% 48%;
    --neutral-600: 197 13% 56%;
    --neutral-500: 198 13% 64%;
    --neutral-400: 195 16% 75%;
    --neutral-300: 195 23% 86%;
    --neutral-200: 195 25% 94%;
    --neutral-100: 200 27% 96%;
    --neutral-50: 210 20% 98%;
    --neutral-white: 0 0% 100%;

    --information-900: 205 100% 21%;
    --information-800: 205 100% 27%;
    --information-700: 205 100% 35%;
    --information-600: 205 100% 45%;
    --information-500: 205 100% 50%;
    --information-400: 205 100% 60%;
    --information-300: 205 100% 66%;
    --information-200: 205 100% 77%;
    --information-100: 205 100% 85%;
    --information-50: 206 100% 95%;

    --warning-900: 25 77% 23%;
    --warning-800: 25 76% 30%;
    --warning-700: 25 77% 38%;
    --warning-600: 25 77% 49%;
    --warning-500: 25 90% 54%;
    --warning-400: 25 89% 63%;
    --warning-300: 25 90% 69%;
    --warning-200: 25 89% 79%;
    --warning-100: 25 89% 86%;
    --warning-50: 26 91% 95%;

    --success-900: 153 97% 14%;
    --success-800: 153 98% 18%;
    --success-700: 153 98% 23%;
    --success-600: 153 97% 29%;
    --success-500: 153 98% 32%;
    --success-400: 153 55% 46%;
    --success-300: 153 46% 54%;
    --success-200: 153 46% 69%;
    --success-100: 153 46% 79%;
    --success-50: 154 47% 93%;

    --error-900: 0 64% 23%;
    --error-800: 0 63% 30%;
    --error-700: 0 63% 38%;
    --error-600: 0 63% 49%;
    --error-500: 0 75% 54%;
    --error-400: 0 75% 64%;
    --error-300: 0 76% 69%;
    --error-200: 0 76% 79%;
    --error-100: 0 75% 86%;
    --error-50: 0 74% 95%;
  }
}

@layer base {
  * {
    @apply border-[hsl(var(--border))];
  }
  body {
    @apply bg-[hsl(var(--background))] text-[hsl(var(--foreground))];
    overscroll-behavior: none;
  }
  html {
    font-family: "Montserrat", sans-serif !important;
    overscroll-behavior: none;
  }
}

/* Toastify custom styles */
.Toastify__toast-theme--light.Toastify__toast--success {
  border-left: 4px solid var(--toastify-color-success);
  background: #f2fff9;
}

.Toastify__toast-theme--light.Toastify__toast--error {
  border-left: 4px solid var(--toastify-color-error);
  background: #fff9f9;
}

.Toastify__toast-theme--light.Toastify__toast--error button {
  color: #dc1911;
}

.Toastify__toast-theme--light.Toastify__toast--warning {
  border-left: 4px solid var(--toastify-color-warning);
  background: #fffff7;
}

.Toastify__toast-theme--light.Toastify__toast--warning button {
  color: #f37820;
}

.Toastify__toast-theme--light.Toastify__toast--info {
  border-left: 4px solid var(--toastify-color-info);
  background: #edf4ff;
}

.Toastify__toast-theme--light {
  color: #383838;
}
