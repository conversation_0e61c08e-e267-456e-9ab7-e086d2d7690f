import { post } from "./common";
import { functionsAxiosInstance } from "@services/axios";

export enum MediaRequestStatus {
  Pending = 0,
  NotFound = 1,
  Downloading = 2,
  Error = 3,
  Ready = 4,
}

export interface MediaRequest {
  apiVersion: string;
  id?: string;
  jobReference?: "DownloadStreamaxVideo" | "DownloadStreamaxTelemetry";
  companyId: string;
  deviceId?: number;
  dvrDeviceId?: number;
  eventId?: number;
  retryCount?: number;
  hasRetry?: boolean;
  deviceCode?: string;
  originalDeviceCode?: string;
  eventDate?: string;
  startDate?: string;
  endDate?: string;
  requestDate?: string;
  downloadDate?: string;
  lastErrorDate?: string;
  status?: MediaRequestStatus;
  cause?: string | null;
  channelsIds: string[];
  filesIds?: string[];
  url?: string;
  contentLength?: number;
  errorCauses?: string[];
  userId?: number | null;
  userName?: string | null;
  externalRequestId?: number | null;
  timeOffset?: number;
  cluster?: number | null;
  streamType?: number;
  requestSource?: string;
  messageCode?: string;
  messageDescription: string;
  codecVersion?: string;
}

export interface CreateMediaRequestParams {
  apiVersion: string;
  userName?: string;
  companyId: string;
  eventId: number;
  deviceId?: number;
  deviceCode: string;
  eventDate: string;
  startDate: string;
  streamType: number;
  endDate: string;
  channelsIds: string[];
  userId?: number;
  requestSource: string;
  messageDescription: string;
}

export const postMediaRequest = async (
  params: CreateMediaRequestParams
): Promise<MediaRequest[]> => {
  console.log({ params });

  // TODO: Add mapper
  const payload: Partial<MediaRequest> = {
    apiVersion: params.apiVersion,
    companyId: params.companyId,
    channelsIds: params.channelsIds,
    requestSource: params.requestSource || "OnDemand",
    messageDescription: params.messageDescription,
    streamType: params.streamType,
  };

  // optional fields
  if (params.eventId) payload.eventId = params.eventId;
  if (params.deviceId) payload.deviceId = params.deviceId;
  if (params.deviceCode) payload.deviceCode = params.deviceCode;
  if (params.eventDate) payload.eventDate = params.eventDate;
  if (params.startDate) payload.startDate = params.startDate;
  if (params.endDate) payload.endDate = params.endDate;
  if (params.userId) payload.userId = params.userId;

  return await post<Partial<MediaRequest>, MediaRequest[]>(
    functionsAxiosInstance,
    "/MediaDownloadRequestFunction",
    payload
  );
};
