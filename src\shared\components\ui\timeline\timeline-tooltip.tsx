import { MdOutlineSchedule } from "react-icons/md";

interface TimelineTooltipProps {
  active?: boolean;
  payload?: Array<{
    payload: { timeInSeconds: number;[key: string]: unknown };
    dataKey?: string;
    value?: number;
    color?: string;
  }>;
  children: React.ReactNode;
  timeFormatter?: (timeInSeconds: number) => string;
}

const defaultTimeFormatter = (timeInSeconds: number): string => {
  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = Math.floor(timeInSeconds % 60);
  return `${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")}`;
};

export const TimelineTooltip = ({
  active,
  payload,
  children,
  timeFormatter = defaultTimeFormatter
}: TimelineTooltipProps) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-neutral-1000 text-white font-semibold px-2 py-1 rounded text-xs">
        <div className="flex items-center gap-1 mb-1">
          <MdOutlineSchedule className="w-4 h-4" />
          <p className="flex font-semibold text-xs">{timeFormatter(data.timeInSeconds)}</p>
        </div>
        {children}
      </div>
    );
  }
  return null;
};

interface TooltipIndicatorProps {
  color: string;
  label: string;
}

export const TooltipIndicator = ({ color, label }: TooltipIndicatorProps) => (
  <div className="flex items-center space-x-1">
    <span className={`w-2 h-2 ${color} rounded-full border border-neutral-white`} />
    <p>{label}</p>
  </div>
);
