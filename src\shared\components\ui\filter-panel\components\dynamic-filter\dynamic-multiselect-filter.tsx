import { useMemo } from "react";
import { DropdownType } from "@components/dropdown";
import { Menu, VirtualizedMenu } from "@components/menu";

interface DynamicMultiselectFilterProps {
  value: string[];
  onValueChange: (value: string[]) => void;
  options: DropdownType[];
  placeholder: string;
  searchable: boolean;
  moveSelectedToTop: boolean;
}

export const DynamicMultiselectFilter = ({
  value,
  onValueChange,
  options,
  placeholder,
  searchable,
  moveSelectedToTop,
}: DynamicMultiselectFilterProps) => {
  const filteredOptions = useMemo(() => {
    const filteredOpts = options;

    if (moveSelectedToTop && value.length > 0) {
      const selectedOptions = filteredOpts.filter((opt) =>
        value.includes(opt.id.toString())
      );
      const otherOptions = filteredOpts.filter(
        (opt) => !value.includes(opt.id.toString())
      );

      if (selectedOptions.length > 0) {
        return [...selectedOptions, ...otherOptions];
      }
    }

    return filteredOpts;
  }, [options, moveSelectedToTop, value]);

  const menuItems = filteredOptions.map((option) => ({
    value: option.id.toString(),
    label: option.text,
  }));

  return (
    <div className="h-full">
      {menuItems.length === 0 ? (
        <div className="flex items-center justify-center h-full text-gray-500">
          No se encontraron opciones
        </div>
      ) : menuItems.length > 20 ? (
        <VirtualizedMenu
          items={menuItems}
          searchable={searchable}
          inputPlaceholder={placeholder}
          selectedItems={value}
          onSelectedItemsChange={onValueChange}
          containerHeight="100%"
        />
      ) : (
        <Menu
          items={menuItems}
          searchable={searchable}
          inputPlaceholder={placeholder}
          selectedItems={value}
          onSelectedItemsChange={onValueChange}
        />
      )}
    </div>
  );
};

DynamicMultiselectFilter.displayName = "DynamicMultiselectFilter";
