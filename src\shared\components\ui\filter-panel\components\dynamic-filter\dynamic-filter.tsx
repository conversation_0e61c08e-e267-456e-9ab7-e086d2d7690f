import { useEffect } from "react";
import { DropdownType } from "@components/dropdown";
import {
  usePendingFilterValue,
  useSetPendingFilter,
  useRegisterFilter,
} from "@components/filter-panel/stores/dynamic-filter-store";
import { getDefaultPlaceholder } from "@components/filter-panel/utils/get-default-placeholder";
import { DynamicSelectFilter } from "./dynamic-select-filter";
import { DynamicMultiselectFilter } from "./dynamic-multiselect-filter";
import { DynamicUnsupportedFilter } from "./dynamic-unsupported-filter";

export interface DynamicFilterProps {
  filterKey: string;
  label: string;
  type: "select" | "multiselect" | "text" | "boolean" | "date" | "daterange";
  options?: DropdownType[];
  placeholder?: string;
  searchable?: boolean;
  // ! dependsOn is used by FilterPanel for dependency logic
  dependsOn?: string | string[]; // dependsOn = filterKeys or built-in filters like "company"
  moveSelectedToTop?: boolean;
}

export const DynamicFilter = ({
  filterKey,
  label,
  type,
  options = [],
  placeholder,
  searchable = false,
  moveSelectedToTop = false,
}: DynamicFilterProps) => {
  const registerFilter = useRegisterFilter();
  const setPendingFilter = useSetPendingFilter();
  const currentValue = usePendingFilterValue<string | string[]>(filterKey);

  useEffect(() => {
    registerFilter(filterKey, {
      type,
      label,
      isActive: false,
    });
  }, [registerFilter, filterKey, label, type]);

  const finalPlaceholder = placeholder || getDefaultPlaceholder(label, type);

  const handleValueChange = (value: string | string[]) =>
    setPendingFilter(filterKey, value);

  switch (type) {
    case "select":
      return (
        <DynamicSelectFilter
          value={typeof currentValue === "string" ? currentValue : ""}
          onValueChange={handleValueChange}
          options={options}
          placeholder={finalPlaceholder}
          searchable={searchable}
          moveSelectedToTop={moveSelectedToTop}
        />
      );

    case "multiselect":
      return (
        <DynamicMultiselectFilter
          value={Array.isArray(currentValue) ? currentValue : []}
          onValueChange={handleValueChange}
          options={options}
          placeholder={finalPlaceholder}
          searchable={searchable}
          moveSelectedToTop={moveSelectedToTop}
        />
      );

    default:
      return <DynamicUnsupportedFilter type={type} />;
  }
};

DynamicFilter.displayName = "DynamicFilter";
