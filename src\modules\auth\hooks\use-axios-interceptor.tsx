import type { AxiosInstance } from "axios";
import { useEffect } from "react";

export const useAxiosInterceptor = ({
  axiosInstance,
  token,
}: {
  axiosInstance: AxiosInstance;
  token: string | undefined;
}) => {
  useEffect(() => {
    if (!token) return;
    axiosInstance.interceptors.request.clear();
    axiosInstance.interceptors.request.use(async (request) => {
      request.headers.Authorization = `Bearer ${token}`;
      return request;
    });
  }, [token, axiosInstance]);
};
