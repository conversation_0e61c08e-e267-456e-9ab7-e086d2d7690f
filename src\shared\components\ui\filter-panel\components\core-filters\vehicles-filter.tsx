import { DropdownType } from "@components/dropdown";
import { VirtualizedRadioGroup } from "@components/radio-group";
import { Input } from "@components/input";
import { SearchIcon } from "lucide-react";
import {
  useFilterStore,
  usePendingVehicle,
} from "@components/filter-panel/stores/filter-store";
import { useFilterableOptions } from "@components/filter-panel/hooks/use-filterable-options";
import { Button } from "@components/button";

interface VehicleFilterProps {
  loading: boolean;
  options: DropdownType[];
  dependsOn?: string | string[];
  moveSelectedToTop?: boolean;
}

export const VehicleFilter = ({
  options,
  moveSelectedToTop = false,
}: // ! dependsOn is used by FilterPanel for dependency logic, not by this component directly
VehicleFilterProps) => {
  const setPendingVehicle = useFilterStore((state) => state.setPendingVehicle);
  const pendingVehicle = usePendingVehicle();

  const { filteredOptions, searchProps } = useFilterableOptions({
    options,
    selectedValue: pendingVehicle,
    moveSelectedToTop,
  });

  const handleRadioChange = (value: string) => setPendingVehicle(value);
  const handleClearSelection = () => setPendingVehicle(null);

  const virtualizedItems = filteredOptions.map((option) => ({
    value: option.id.toString(),
    label: option.text,
  }));

  return (
    <div className="h-full flex flex-col">
      <div className="mb-3 flex-shrink-0">
        <div className="relative">
          <SearchIcon className="absolute left-2 top-1/2 -translate-y-1/2 text-neutral-600 size-4" />
          <Input
            placeholder="Buscar vehículo"
            className="pl-8"
            {...searchProps}
          />
        </div>
      </div>

      {virtualizedItems.length === 0 ? (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          No se encontraron vehículos
        </div>
      ) : (
        <div className="flex-1 min-h-0">
          <VirtualizedRadioGroup
            value={pendingVehicle || ""}
            onValueChange={handleRadioChange}
            items={virtualizedItems}
            containerHeight="100%"
            className="h-full"
          />
        </div>
      )}

      <div className="flex-shrink-0 pt-2 flex justify-end">
        <Button
          variant="link"
          onClick={handleClearSelection}
          disabled={!pendingVehicle}
          className={!pendingVehicle ? "opacity-50 cursor-not-allowed" : ""}
        >
          Limpiar seleccionado
        </Button>
      </div>
    </div>
  );
};

VehicleFilter.displayName = "VehicleFilter";
