/// <reference types="vitest" />
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import mkcert from "vite-plugin-mkcert";
import path from "node:path";
import version from "vite-plugin-package-version";
import tailwindcss from "@tailwindcss/vite";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss(), mkcert(), version()],
  base: "",
  build: {
    assetsDir: "assets",
  },
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: "./src/test/setup.ts",
    coverage: {
      provider: "v8",
      reporter: ["text", "html"],
      exclude: ["node_modules/", "src/test/setup.ts"],
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
      "@auth": path.resolve(__dirname, "src/modules/auth"),
      "@components": path.resolve(__dirname, "src/shared/components/ui"),
      "@constants": path.resolve(__dirname, "src/shared/constants"),
      "@forms": path.resolve(__dirname, "src/shared/forms"),
      "@hooks": path.resolve(__dirname, "src/shared/hooks"),
      "@layout": path.resolve(__dirname, "src/layout"),
      "@services": path.resolve(__dirname, "src/shared/services"),
      "@pages": path.resolve(__dirname, "src/pages"),
      "@providers": path.resolve(__dirname, "src/shared/providers"),
      "@stores": path.resolve(__dirname, "src/shared/stores"),
      "@styles": path.resolve(__dirname, "src/styles"),
    },
  },
});
