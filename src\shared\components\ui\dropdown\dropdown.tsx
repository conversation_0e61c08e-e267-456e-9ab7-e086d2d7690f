import { useFormContext, useWatch } from "react-hook-form";

import { useFormRulesContext } from "@/shared/forms/form-rules";

import { getSelectStyles } from "./styles";
import type { ReactNode } from "react";

type DropDownId = number | string;

export type DropdownType = {
  id: DropDownId;
  text: string;
  key?: string;
  disabled?: boolean;
  selected?: boolean;
  extra?: Record<string, unknown>;
};

export type DropdownProps = {
  name: string;
  options: DropdownType[];
  placeholder?: string;
  leftIcon?: ReactNode;
  hasError?: boolean;
  isDisabled?: boolean;
  isReadonly?: boolean;
  isLoading?: boolean;
  hasEmptyValue?: boolean;
};

export const Dropdown = ({
  name,
  options,
  placeholder,
  leftIcon,
  hasError = false,
  isDisabled = false,
  isReadonly = false,
  isLoading = false,
  hasEmptyValue = false,
}: DropdownProps) => {
  const methods = useFormContext();
  const rule = useFormRulesContext();

  const selectedValue = useWatch({
    name,
    control: methods.control,
  }) as DropDownId;

  const isDropdownDisabled = isDisabled || isReadonly || options.length === 0;

  return (
    <div className="flex items-center pl-3 border-solid border-2 border-gray-500 rounded bg-white">
      {leftIcon || null}

      <select
        className={`${getSelectStyles(hasError, isReadonly)} border-none`}
        value={selectedValue || ""}
        {...methods.register(name, rule(name))}
        disabled={isDropdownDisabled}
      >
        {isLoading ? (
          <option value="" disabled>
            Cargando...
          </option>
        ) : (
          <option value="" disabled={!hasEmptyValue}>
            {placeholder}
          </option>
        )}

        {options.map((item) => (
          <option
            key={item.key ?? item.id}
            value={item.id}
            disabled={item.disabled}
          >
            {item.text}
          </option>
        ))}
      </select>
    </div>
  );
};
