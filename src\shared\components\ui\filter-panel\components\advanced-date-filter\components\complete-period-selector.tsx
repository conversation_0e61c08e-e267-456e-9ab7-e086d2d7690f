import React, { useState } from 'react';
import { Button } from '@/shared/components/ui/button';
import { cn } from '@/styles/cn';
import { CompletePeriod } from '../types/advanced-date-filter.types';
import { formatPeriodLabel, getMonthsInYear, getAvailableYears } from '../utils/date-helpers';
import { X, Calendar, ChevronDown, ChevronUp } from 'lucide-react';

interface CompletePeriodSelectorProps {
  selectedPeriods: CompletePeriod[];
  onAddPeriod: (year: number, month: number) => void;
  onRemovePeriod: (periodId: string) => void;
  onClearAll: () => void;
  maxPeriods?: number;
  disabled?: boolean;
  className?: string;
}

export const CompletePeriodSelector: React.FC<CompletePeriodSelectorProps> = ({
  selectedPeriods,
  onAddPeriod,
  onRemovePeriod,
  onClearAll,
  maxPeriods = 12,
  disabled = false,
  className
}) => {
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [isYearSelectorOpen, setIsYearSelectorOpen] = useState(false);

  const availableYears = getAvailableYears();
  const monthsInYear = getMonthsInYear(selectedYear);

  const isMonthSelected = (year: number, month: number): boolean => {
    return selectedPeriods.some(p => p.year === year && p.month === month);
  };

  const isMaxPeriodsReached = selectedPeriods.length >= maxPeriods;

  const handleMonthClick = (month: number) => {
    if (disabled) return;

    if (isMonthSelected(selectedYear, month)) {
      const period = selectedPeriods.find(p => p.year === selectedYear && p.month === month);
      if (period) {
        onRemovePeriod(period.id);
      }
    } else if (!isMaxPeriodsReached) {
      onAddPeriod(selectedYear, month);
    }
  };

  const handleYearChange = (year: number) => {
    setSelectedYear(year);
    setIsYearSelectorOpen(false);
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header with year selector */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Calendar className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-900">
            Seleccionar Períodos Completos
          </span>
        </div>
        
        {selectedPeriods.length > 0 && (
          <Button
            variant="link"
            size="sm"
            onClick={onClearAll}
            disabled={disabled}
            className="text-red-600 hover:text-red-700"
          >
            Limpiar todo
          </Button>
        )}
      </div>

      {/* Year selector */}
      <div className="relative">
        <Button
          variant="secondary"
          onClick={() => setIsYearSelectorOpen(!isYearSelectorOpen)}
          disabled={disabled}
          className="w-full justify-between border border-gray-300"
        >
          <span>Año: {selectedYear}</span>
          {isYearSelectorOpen ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </Button>
        
        {isYearSelectorOpen && (
          <div className="absolute top-full left-0 right-0 z-10 mt-1 bg-white border border-gray-200 rounded-md shadow-lg">
            {availableYears.map(year => (
              <button
                key={year}
                onClick={() => handleYearChange(year)}
                className={cn(
                  "w-full px-3 py-2 text-left hover:bg-gray-50 transition-colors",
                  year === selectedYear && "bg-primary-50 text-primary-700"
                )}
              >
                {year}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Month grid */}
      <div className="grid grid-cols-3 gap-2">
        {monthsInYear.map(({ month, label, disabled: monthDisabled }) => {
          const isSelected = isMonthSelected(selectedYear, month);
          const isDisabled = disabled || monthDisabled || (!isSelected && isMaxPeriodsReached);

          return (
            <button
              key={month}
              onClick={() => handleMonthClick(month)}
              disabled={isDisabled}
              className={cn(
                "p-3 text-sm rounded-lg border transition-all duration-200",
                "hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1",
                isSelected
                  ? "bg-primary-600 text-white border-primary-600 shadow-sm"
                  : "bg-white text-gray-700 border-gray-200 hover:border-primary-300 hover:bg-primary-50",
                isDisabled && "opacity-50 cursor-not-allowed hover:bg-white hover:border-gray-200"
              )}
            >
              {label}
            </button>
          );
        })}
      </div>

      {/* Selection summary */}
      {selectedPeriods.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              Períodos seleccionados ({selectedPeriods.length}/{maxPeriods})
            </span>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {selectedPeriods
              .sort((a, b) => a.year - b.year || a.month - b.month)
              .map(period => (
                <div
                  key={period.id}
                  className="flex items-center space-x-1 bg-primary-100 text-primary-800 px-2 py-1 rounded-md text-xs"
                >
                  <span>{formatPeriodLabel(period)}</span>
                  <button
                    onClick={() => onRemovePeriod(period.id)}
                    disabled={disabled}
                    className="hover:bg-primary-200 rounded-full p-0.5 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Limits warning */}
      {isMaxPeriodsReached && (
        <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
          Has alcanzado el límite máximo de {maxPeriods} períodos. 
          Elimina algunos períodos para seleccionar otros.
        </div>
      )}

      {/* Help text */}
      <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
        <strong>Períodos Completos:</strong> Cada período incluye el mes completo desde el día 1 
        hasta el último día del mes. Ideal para análisis mensuales regulares.
      </div>
    </div>
  );
};

CompletePeriodSelector.displayName = 'CompletePeriodSelector';