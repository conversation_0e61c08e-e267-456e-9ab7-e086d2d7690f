import React, { type PropsWithChildren, useMemo } from "react";
import type { RegisterOptions } from "react-hook-form";

export type FormRulesType = (name: string) => RegisterOptions;

export const FormRulesContext = React.createContext<FormRulesType>(() => ({}));

export const useFormRulesContext = () => React.useContext(FormRulesContext);

export type FromRulesProps = PropsWithChildren<{
  rules: { [id: string]: RegisterOptions };
}>;

const getRuleOrThrown = (
  name: string,
  fields: { [id: string]: RegisterOptions }
): RegisterOptions => {
  if (!(name in fields)) {
    throw Error(
      `ASSERTION ERROR: the field with name '${name}' does not exists in the current context`
    );
  }

  return fields[name];
};

export const FormRules = ({ rules: fields, children }: FromRulesProps) => {
  const rule = useMemo(
    () => (name: string) => getRuleOrThrown(name, fields),
    [fields]
  );
  return (
    <FormRulesContext.Provider value={rule}>
      {children}
    </FormRulesContext.Provider>
  );
};
