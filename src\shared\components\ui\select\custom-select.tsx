import { useFormContext, useWatch } from "react-hook-form";

import type { ReactNode } from "react";
import { useEffect, useState } from "react";
import { cn } from "@styles/cn";
import { Label } from "@components/label";
import { ChevronDown, ChevronUp } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./select";
import { useFormRulesContext } from "@/shared/forms/form-rules";

type CustomSelectId = number | string;

export type CustomSelectOptionType<TExtra = Record<string, unknown>> = {
  id: CustomSelectId;
  text: string;
  key?: string;
  disabled?: boolean;
  selected?: boolean;
  extra?: TExtra;
};

export type CustomSelectProps<TExtra = Record<string, unknown>> = {
  name: string;
  options: CustomSelectOptionType<TExtra>[];
  placeholder?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  label?: string;
  required?: boolean;
  hasError?: boolean;
  isDisabled?: boolean;
  isReadonly?: boolean;
  isLoading?: boolean;
  hasEmptyValue?: boolean;
  className?: string;
  dynamicLeftContent?: (option: CustomSelectOptionType<TExtra>) => ReactNode;
  showErrors?: boolean;
  textFormatter?: (text: string) => ReactNode;
};

export const CustomSelect = ({
  name,
  options,
  placeholder = "Select option...",
  leftIcon,
  rightIcon,
  label,
  required = false,
  isDisabled = false,
  isReadonly = false,
  isLoading = false,
  hasEmptyValue = false,
  className,
  dynamicLeftContent,
  showErrors = false,
  textFormatter,
}: CustomSelectProps) => {
  const [open, setOpen] = useState(false);
  const methods = useFormContext();
  const rule = useFormRulesContext();

  const selectedValue = useWatch({
    name,
    control: methods.control,
  }) as CustomSelectId;

  const errors = showErrors ? methods.formState.errors : {};

  const isSelectDisabled = isDisabled || isReadonly || options.length === 0;

  const selectedOption = options.find((option) => option.id === selectedValue);

  const handleValueChange = (value: string) => {
    if (value === "") {
      methods.setValue(name, "");
      if (showErrors) {
        methods.clearErrors(name);
      }
      return;
    }

    const selectedOption = options.find(
      (option) => option.id.toString() === value
    );
    if (!selectedOption) return;

    methods.setValue(name, selectedOption.id);
    if (showErrors) {
      methods.clearErrors(name);
    }
  };

  useEffect(() => {
    methods.register(name, rule(name));
  }, [methods, name, rule]);

  return (
    <div className={cn("w-full", className)}>
      {label && (
        <Label htmlFor={name}>
          {label}
          {required && <span className="text-red-500"> *</span>}
        </Label>
      )}

      <Select
        value={selectedValue?.toString() || ""}
        onValueChange={handleValueChange}
        disabled={isSelectDisabled}
        open={open}
        onOpenChange={setOpen}
      >
        <SelectTrigger
          hideIcon
          className={cn(
            showErrors &&
              errors[name] &&
              "border-red-500 disabled:border-red-500",
            isReadonly && "bg-gray-100 cursor-default",
            leftIcon && "pl-3",
            rightIcon && "pr-3"
          )}
        >
          <div className="flex items-center w-full">
            {leftIcon && <div className="mr-2 flex-shrink-0">{leftIcon}</div>}
            <div className="flex-1 text-left">
              {isLoading ? (
                "Cargando..."
              ) : selectedOption ? (
                <div className="flex items-center">
                  {dynamicLeftContent && dynamicLeftContent(selectedOption)}
                  <div className="font-medium">
                    {selectedOption.text.length > 25 ? (
                      selectedOption.text.substring(0, 25) + "..."
                    ) : textFormatter ? (
                      textFormatter(selectedOption.text)
                    ) : (
                      <span className="text-dark-gray-1000">
                        {selectedOption.text}
                      </span>
                    )}
                  </div>
                </div>
              ) : (
                <SelectValue placeholder={placeholder} />
              )}
            </div>
            {rightIcon && <div className="ml-2 flex-shrink-0">{rightIcon}</div>}
            {open ? (
              <ChevronUp
                className="ml-2 h-4 w-4 shrink-0 opacity-50"
                color={`${showErrors && errors[name] && "hsl(20 89% 58%)"}`}
              />
            ) : (
              <ChevronDown
                className="ml-2 h-4 w-4 shrink-0 opacity-50"
                color={`${showErrors && errors[name] && "hsl(20 89% 58%)"}`}
              />
            )}
          </div>
        </SelectTrigger>
        <SelectContent className="bg-white">
          {hasEmptyValue && <SelectItem value="">{placeholder}</SelectItem>}
          {options.map((option) => (
            <SelectItem
              key={option.key ?? option.id}
              value={option.id.toString()}
              disabled={option.disabled}
            >
              <div className="flex items-center">
                {dynamicLeftContent && dynamicLeftContent(option)}
                {textFormatter ? textFormatter(option.text) : option.text}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {showErrors && errors[name] && (
        <p className="text-error-500 text-sm font-medium mt-1">
          {errors[name]?.message as string}
        </p>
      )}
    </div>
  );
};
