import React, { useState } from 'react';
import { Calendar } from '@/shared/components/ui/calendar';
import { Button } from '@/shared/components/ui/button';
import { cn } from '@/styles/cn';
import { FreeSelection } from '../types/advanced-date-filter.types';
import { formatDateRange } from '../utils/date-helpers';
import { X, Calendar as CalendarIcon, Plus } from 'lucide-react';
import { format } from 'date-fns';

interface GranularDateSelectorProps {
  freeSelection: FreeSelection;
  onAddSpecificDate: (date: Date) => void;
  onRemoveSpecificDate: (date: Date) => void;
  onAddDateRange: (startDate: Date, endDate: Date) => void;
  onRemoveDateRange: (rangeId: string) => void;
  onSetMode: (mode: 'specific-days' | 'date-ranges') => void;
  onClearAll: () => void;
  maxDates?: number;
  disabled?: boolean;
  className?: string;
}

export const GranularDateSelector: React.FC<GranularDateSelectorProps> = ({
  freeSelection,
  onAddSpecificDate,
  onRemoveSpecificDate,
  onAddDateRange,
  onRemoveDateRange,
  onSetMode,
  onClearAll,
  maxDates = 31,
  disabled = false,
  className
}) => {
  const [rangeStart, setRangeStart] = useState<Date | null>(null);
  const [rangeEnd, setRangeEnd] = useState<Date | null>(null);

  const totalSelectedDates = freeSelection.selectedDates.length + 
    freeSelection.dateRanges.reduce((acc, range) => {
      const days = Math.ceil((range.endDate.getTime() - range.startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      return acc + days;
    }, 0);

  const isMaxDatesReached = totalSelectedDates >= maxDates;

  const handleDateSelect = (date: Date | undefined) => {
    if (!date || disabled) return;

    if (freeSelection.mode === 'specific-days') {
      const isSelected = freeSelection.selectedDates.some(d => 
        d.getTime() === date.getTime()
      );

      if (isSelected) {
        onRemoveSpecificDate(date);
      } else if (!isMaxDatesReached) {
        onAddSpecificDate(date);
      }
    } else {
      // Date range mode
      if (!rangeStart) {
        setRangeStart(date);
        setRangeEnd(null);
      } else if (!rangeEnd) {
        if (date >= rangeStart) {
          setRangeEnd(date);
        } else {
          setRangeStart(date);
          setRangeEnd(null);
        }
      } else {
        // Reset and start new range
        setRangeStart(date);
        setRangeEnd(null);
      }
    }
  };

  const handleAddRange = () => {
    if (rangeStart && rangeEnd && !disabled) {
      onAddDateRange(rangeStart, rangeEnd);
      setRangeStart(null);
      setRangeEnd(null);
    }
  };

  const handleCancelRange = () => {
    setRangeStart(null);
    setRangeEnd(null);
  };

  const isDateSelected = (date: Date): boolean => {
    if (freeSelection.mode === 'specific-days') {
      return freeSelection.selectedDates.some(d => d.getTime() === date.getTime());
    }
    
    // Check if date is in any range
    return freeSelection.dateRanges.some(range => 
      date >= range.startDate && date <= range.endDate
    );
  };

  const isDateInPendingRange = (date: Date): boolean => {
    if (freeSelection.mode !== 'date-ranges' || !rangeStart) return false;
    
    if (!rangeEnd) {
      return date.getTime() === rangeStart.getTime();
    }
    
    return date >= rangeStart && date <= rangeEnd;
  };

  const hasSelections = freeSelection.selectedDates.length > 0 || freeSelection.dateRanges.length > 0;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <CalendarIcon className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-900">
            Selección Libre de Fechas
          </span>
        </div>
        
        {hasSelections && (
          <Button
            variant="link"
            size="sm"
            onClick={onClearAll}
            disabled={disabled}
            className="text-red-600 hover:text-red-700"
          >
            Limpiar todo
          </Button>
        )}
      </div>

      {/* Mode toggle */}
      <div className="flex space-x-2">
        <Button
          variant={freeSelection.mode === 'specific-days' ? 'primary' : 'secondary'}
          size="sm"
          onClick={() => onSetMode('specific-days')}
          disabled={disabled}
          className="flex-1"
        >
          Días Específicos
        </Button>
        <Button
          variant={freeSelection.mode === 'date-ranges' ? 'primary' : 'secondary'}
          size="sm"
          onClick={() => onSetMode('date-ranges')}
          disabled={disabled}
          className="flex-1"
        >
          Rangos de Fechas
        </Button>
      </div>

      {/* Calendar */}
      <div className="flex justify-center">
        <Calendar
          mode="single"
          selected={undefined}
          onSelect={handleDateSelect}
          disabled={{
            after: new Date(),
          }}
          className="rounded-md border"
          modifiers={{
            selected: isDateSelected,
            pending: isDateInPendingRange,
          }}
          modifiersClassNames={{
            selected: "bg-primary-600 text-white hover:bg-primary-700",
            pending: "bg-primary-200 text-primary-800",
          }}
        />
      </div>

      {/* Range controls (only in date-ranges mode) */}
      {freeSelection.mode === 'date-ranges' && (rangeStart || rangeEnd) && (
        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
          <div className="text-sm font-medium text-blue-900 mb-2">
            Seleccionando Rango
          </div>
          <div className="text-sm text-blue-700 mb-3">
            {rangeStart && !rangeEnd && (
              <>Inicio: {format(rangeStart, 'dd/MM/yyyy')} - Selecciona fecha de fin</>
            )}
            {rangeStart && rangeEnd && (
              <>Rango: {format(rangeStart, 'dd/MM/yyyy')} - {format(rangeEnd, 'dd/MM/yyyy')}</>
            )}
          </div>
          <div className="flex space-x-2">
            {rangeStart && rangeEnd && (
              <Button
                variant="primary"
                size="sm"
                onClick={handleAddRange}
                disabled={disabled || isMaxDatesReached}
              >
                <Plus className="h-3 w-3 mr-1" />
                Agregar Rango
              </Button>
            )}
            <Button
              variant="secondary"
              size="sm"
              onClick={handleCancelRange}
              disabled={disabled}
            >
              Cancelar
            </Button>
          </div>
        </div>
      )}

      {/* Selection summary */}
      {hasSelections && (
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              Total de días seleccionados: {totalSelectedDates}/{maxDates}
            </span>
          </div>

          {/* Specific dates */}
          {freeSelection.selectedDates.length > 0 && (
            <div>
              <div className="text-xs font-medium text-gray-700 mb-2">
                Días Específicos ({freeSelection.selectedDates.length})
              </div>
              <div className="flex flex-wrap gap-1">
                {freeSelection.selectedDates
                  .sort((a, b) => a.getTime() - b.getTime())
                  .map((date, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-1 bg-green-100 text-green-800 px-2 py-1 rounded text-xs"
                    >
                      <span>{format(date, 'dd/MM/yyyy')}</span>
                      <button
                        onClick={() => onRemoveSpecificDate(date)}
                        disabled={disabled}
                        className="hover:bg-green-200 rounded-full p-0.5 transition-colors"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  ))}
              </div>
            </div>
          )}

          {/* Date ranges */}
          {freeSelection.dateRanges.length > 0 && (
            <div>
              <div className="text-xs font-medium text-gray-700 mb-2">
                Rangos de Fechas ({freeSelection.dateRanges.length})
              </div>
              <div className="space-y-1">
                {freeSelection.dateRanges.map((range) => (
                  <div
                    key={range.id}
                    className="flex items-center justify-between bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
                  >
                    <span>{formatDateRange(range)}</span>
                    <button
                      onClick={() => onRemoveDateRange(range.id)}
                      disabled={disabled}
                      className="hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Limits warning */}
      {isMaxDatesReached && (
        <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
          Has alcanzado el límite máximo de {maxDates} días. 
          Elimina algunas fechas para seleccionar otras.
        </div>
      )}

      {/* Help text */}
      <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
        <strong>Selección Libre:</strong> 
        {freeSelection.mode === 'specific-days' 
          ? ' Haz clic en días específicos del calendario para seleccionarlos individualmente.'
          : ' Haz clic en una fecha de inicio, luego en una fecha de fin para crear un rango.'
        }
      </div>
    </div>
  );
};

GranularDateSelector.displayName = 'GranularDateSelector';