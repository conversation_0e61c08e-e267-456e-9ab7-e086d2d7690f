import { useState, useEffect, useCallback } from 'react';
import { PersistentConfig, AdvancedDateFilterState, CompletePeriod } from '../types/advanced-date-filter.types';

const STORAGE_KEY = 'advanced-date-filter-config';
const RECENT_SELECTIONS_KEY = 'advanced-date-filter-recent';

const defaultConfig: PersistentConfig = {
  defaultMode: 'complete-periods',
  favoritePeriodsPresets: [],
  recentSelections: [],
  showValidationHints: true,
  compactView: false,
  maxSelectablePeriods: 12,
  maxSelectableDates: 31
};

export const usePersistentConfig = () => {
  const [config, setConfig] = useState<PersistentConfig>(defaultConfig);
  const [isLoading, setIsLoading] = useState(true);

  // Load config from localStorage on mount
  useEffect(() => {
    try {
      const savedConfig = localStorage.getItem(STORAGE_KEY);
      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig);
        setConfig({ ...defaultConfig, ...parsedConfig });
      }
    } catch (error) {
      console.warn('Failed to load advanced date filter config:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save config to localStorage
  const saveConfig = useCallback((newConfig: Partial<PersistentConfig>) => {
    try {
      const updatedConfig = { ...config, ...newConfig };
      setConfig(updatedConfig);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedConfig));
    } catch (error) {
      console.warn('Failed to save advanced date filter config:', error);
    }
  }, [config]);

  // Save recent selection
  const saveRecentSelection = useCallback((selection: AdvancedDateFilterState) => {
    try {
      const recentSelections = [...config.recentSelections];
      
      // Remove duplicate if exists
      const existingIndex = recentSelections.findIndex(s => 
        JSON.stringify(s) === JSON.stringify(selection)
      );
      
      if (existingIndex !== -1) {
        recentSelections.splice(existingIndex, 1);
      }
      
      // Add to beginning
      recentSelections.unshift(selection);
      
      // Keep only last 10 selections
      const trimmedSelections = recentSelections.slice(0, 10);
      
      saveConfig({ recentSelections: trimmedSelections });
    } catch (error) {
      console.warn('Failed to save recent selection:', error);
    }
  }, [config.recentSelections, saveConfig]);

  // Save favorite preset
  const saveFavoritePreset = useCallback((periods: CompletePeriod[], name?: string) => {
    try {
      const presetName = name || `Preset ${config.favoritePeriodsPresets.length + 1}`;
      const newPreset = periods.map(p => ({ ...p, name: presetName }));
      
      const updatedPresets = [...config.favoritePeriodsPresets, newPreset];
      
      // Keep only last 5 presets
      const trimmedPresets = updatedPresets.slice(-5);
      
      saveConfig({ favoritePeriodsPresets: trimmedPresets });
    } catch (error) {
      console.warn('Failed to save favorite preset:', error);
    }
  }, [config.favoritePeriodsPresets, saveConfig]);

  // Remove favorite preset
  const removeFavoritePreset = useCallback((index: number) => {
    try {
      const updatedPresets = config.favoritePeriodsPresets.filter((_, i) => i !== index);
      saveConfig({ favoritePeriodsPresets: updatedPresets });
    } catch (error) {
      console.warn('Failed to remove favorite preset:', error);
    }
  }, [config.favoritePeriodsPresets, saveConfig]);

  // Clear all data
  const clearAllData = useCallback(() => {
    try {
      localStorage.removeItem(STORAGE_KEY);
      localStorage.removeItem(RECENT_SELECTIONS_KEY);
      setConfig(defaultConfig);
    } catch (error) {
      console.warn('Failed to clear advanced date filter data:', error);
    }
  }, []);

  // Reset to defaults
  const resetToDefaults = useCallback(() => {
    saveConfig(defaultConfig);
  }, [saveConfig]);

  return {
    config,
    isLoading,
    saveConfig,
    saveRecentSelection,
    saveFavoritePreset,
    removeFavoritePreset,
    clearAllData,
    resetToDefaults
  };
};

// Hook for managing user preferences
export const useUserPreferences = () => {
  const { config, saveConfig } = usePersistentConfig();

  const toggleValidationHints = useCallback(() => {
    saveConfig({ showValidationHints: !config.showValidationHints });
  }, [config.showValidationHints, saveConfig]);

  const toggleCompactView = useCallback(() => {
    saveConfig({ compactView: !config.compactView });
  }, [config.compactView, saveConfig]);

  const setDefaultMode = useCallback((mode: 'complete-periods' | 'free-selection') => {
    saveConfig({ defaultMode: mode });
  }, [saveConfig]);

  const setMaxLimits = useCallback((maxPeriods: number, maxDates: number) => {
    saveConfig({ 
      maxSelectablePeriods: maxPeriods,
      maxSelectableDates: maxDates 
    });
  }, [saveConfig]);

  return {
    preferences: {
      showValidationHints: config.showValidationHints,
      compactView: config.compactView,
      defaultMode: config.defaultMode,
      maxSelectablePeriods: config.maxSelectablePeriods,
      maxSelectableDates: config.maxSelectableDates
    },
    actions: {
      toggleValidationHints,
      toggleCompactView,
      setDefaultMode,
      setMaxLimits
    }
  };
};

// Hook for managing recent selections
export const useRecentSelections = () => {
  const { config, saveRecentSelection } = usePersistentConfig();

  const addRecentSelection = useCallback((selection: AdvancedDateFilterState) => {
    // Only save if it has actual selections
    const hasSelections = selection.mode === 'complete-periods' 
      ? selection.completePeriods.length > 0
      : selection.freeSelection.selectedDates.length > 0 || 
        selection.freeSelection.dateRanges.length > 0;

    if (hasSelections && selection.isValid) {
      saveRecentSelection(selection);
    }
  }, [saveRecentSelection]);

  const getRecentSelections = useCallback(() => {
    return config.recentSelections.slice(0, 5); // Return only last 5
  }, [config.recentSelections]);

  return {
    recentSelections: getRecentSelections(),
    addRecentSelection
  };
};

// Hook for managing favorite presets
export const useFavoritePresets = () => {
  const { config, saveFavoritePreset, removeFavoritePreset } = usePersistentConfig();

  const addFavoritePreset = useCallback((periods: CompletePeriod[], name?: string) => {
    if (periods.length > 0) {
      saveFavoritePreset(periods, name);
    }
  }, [saveFavoritePreset]);

  return {
    favoritePresets: config.favoritePeriodsPresets,
    addFavoritePreset,
    removeFavoritePreset
  };
};