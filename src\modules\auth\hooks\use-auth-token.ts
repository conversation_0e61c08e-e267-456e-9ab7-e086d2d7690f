import { useQuery } from "@tanstack/react-query";
import { jwtDecode } from "jwt-decode";
import { getAuthToken } from "../utils/get-auth-token";
import { useAxiosInterceptor } from "./use-axios-interceptor";
import { useTokenMessageListener } from "./use-token-message-listener";
import { billingServiceAxiosInstance } from "@services/axios";
import { authTokenMapper } from "../utils";

export const useAuthToken = () => {
  const {
    data: token,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["authToken"],
    queryFn: getAuthToken,
  });

  useAxiosInterceptor({
    axiosInstance: billingServiceAxiosInstance,
    token,
  });
  useTokenMessageListener();

  const status = isLoading
    ? "loading"
    : token
    ? "authenticated"
    : "unauthenticated";

  const credentials = token
    ? authTokenMapper(jwtDecode(token as string))
    : null;

  return {
    token,
    status,
    credentials,
    error,
  };
};
