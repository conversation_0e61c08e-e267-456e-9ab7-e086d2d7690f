import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cn } from "@styles/cn";
import type { VariantProps } from "class-variance-authority";
import * as React from "react";

import { tabsListVariants } from "./tabs-list.styles";

type TabsListProps = {
  size?: VariantProps<typeof tabsListVariants>["size"];
} & React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>;

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  TabsListProps
>(({ className, size, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(tabsListVariants({ size }), className)}
    {...props}
  />
));

TabsList.displayName = "TabsList";

export { TabsList };
