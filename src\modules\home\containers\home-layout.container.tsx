import { HomeLayoutView } from "../views/home-layout.view";
import { FilterView } from "../components/filter.view";
import { MdOutlineFileDownload } from "react-icons/md";

import { ButtonWithIcon } from "@/shared/components/ui/button/button-with-icon";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/shared/components/ui/tooltip";

import { ModuleHeader } from "@/shared/components";
import { IntegrationInformationDrawer } from "../components/integration-information-drawer";

export const HomeLayoutContainer = () => {
  return (
    <HomeLayoutView>
      <ModuleHeader title="Reportes F14 & F34">
        <IntegrationInformationDrawer />
        <Tooltip>
          <TooltipTrigger>
            <ButtonWithIcon
              variant="link"
              icon={<MdOutlineFileDownload />}
              text="Exportar"
              disabled
            />
          </TooltipTrigger>
          <TooltipContent>TBD</TooltipContent>
        </Tooltip>
        <FilterView />
      </ModuleHeader>
    </HomeLayoutView>
  );
};
