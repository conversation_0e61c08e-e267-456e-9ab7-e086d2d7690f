import { useState } from "react";
import { MdCheck } from "react-icons/md";

import { Popover, PopoverContent, PopoverTrigger } from "../popover/popover";

import ButtonPopup from "./button-popup";
import { cn } from "@/styles/cn";

export default function ColumnsPopup({
  columns,
  icon,
  text,
  multiselect = true,
  onSelectionChange,
  currentSelection,
}: {
  columns: { id: string; label: string }[];
  icon: React.ReactNode;
  text: string;
  multiselect?: boolean;
  onSelectionChange: (columns: string[]) => void;
  currentSelection: string[];
}) {
  const [actionsOpen, setActionsOpen] = useState(false);

  return (
    <Popover open={actionsOpen} onOpenChange={setActionsOpen}>
      <PopoverTrigger className="w-full">
        <ButtonPopup
          className={cn(actionsOpen && "bg-[#FAF5FF]")}
          icon={icon}
          text={text}
        />
      </PopoverTrigger>
      <PopoverContent
        className="mr-1 mt-11 flex w-[130px] flex-col items-start p-1"
        side="left"
      >
        {columns.map((column) => (
          <ButtonPopup
            key={column.id}
            className="pl-4"
            icon={
              <MdCheck
                className={cn(
                  currentSelection.includes(column.id)
                    ? "opacity-100"
                    : "opacity-0"
                )}
              />
            }
            text={column.label}
            onClick={() => {
              if (multiselect) {
                if (currentSelection.includes(column.id)) {
                  onSelectionChange(
                    currentSelection.filter((id) => id !== column.id)
                  );
                } else {
                  onSelectionChange([...currentSelection, column.id]);
                }
              } else {
                if (currentSelection.includes(column.id)) {
                  onSelectionChange([]);
                } else {
                  onSelectionChange([column.id]);
                }
              }
            }}
          />
        ))}
      </PopoverContent>
    </Popover>
  );
}
