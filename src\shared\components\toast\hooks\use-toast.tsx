import type { TypeOptions } from "react-toastify";
import { useCallback } from "react";
import { toast } from "react-toastify";
import { Button } from "@components/button";

export function useToast() {
  const showToast = useCallback(
    (
      {
        type,
        theme,
        title,
        description,
        actionText,
        action,
        containerId,
      }: {
      type: TypeOptions,
      theme: "light" | "colored",
      title: string,
      description?: string,
      actionText?: string,
      action?: () => void,
      containerId?: string,
    }
    ) => {
      toast(
        <div className="ml-2 flex flex-col w-[30rem]">
          <span className="text-sm font-semibold">{title}</span>
          <span className="text-xs">{description}</span>
          {action && actionText && (
            <Button
              className="ml-auto mt-1 w-32 font-semibold hover:bg-slate-200 hover:text-black"
              variant="link"
              onClick={action}
            >
              {actionText}
            </Button>
          )}
        </div>,
        {
          type,
          theme,
          position: "top-right",
          ...(containerId && { containerId }),
        },
      );
    },
    [],
  );

  return {
    showToast,
  };
}
