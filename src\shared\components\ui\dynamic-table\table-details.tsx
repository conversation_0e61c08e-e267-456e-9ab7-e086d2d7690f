import { Row } from "@tanstack/react-table";
import { motion } from "framer-motion";
import React from "react";
import { MdClose } from "react-icons/md";

import { TableData } from "./data-table";
import { Button } from "../button/button";

export default function TableDetails<TData>({
  selectedRowDetails,
  onRenderDetails,
  setSelectedRowDetails,
}: {
  selectedRowDetails: Row<TableData<TData>> | undefined;
  onRenderDetails?: (row: Row<TableData<TData>>) => JSX.Element;
  setSelectedRowDetails: React.Dispatch<
    React.SetStateAction<Row<TableData<TData>> | undefined>
  >;
}) {
  return (
    <motion.div
      animate={
        selectedRowDetails && {
          opacity: 1,
          width: "20rem",
        }
      }
      className="flex overflow-hidden"
      exit={{
        opacity: 1,
        width: "20rem",
      }}
      initial={{
        opacity: 0,
        width: "0%",
      }}
      transition={{
        duration: 0.2,
      }}
    >
      <div className="w-80 pb-16 pl-6">
        <div className="relative flex w-auto rounded border border-[#DACEE6] bg-white p-4">
          <div className="absolute right-2 top-2">
            <Button
              className="size-8 rounded-full p-2"
              variant="link"
              onClick={() => setSelectedRowDetails(undefined)}
            >
              <MdClose fontSize={32} />
            </Button>
          </div>
          {selectedRowDetails && onRenderDetails?.(selectedRowDetails)}
        </div>
      </div>
    </motion.div>
  );
}
