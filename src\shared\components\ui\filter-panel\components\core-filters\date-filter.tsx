import { cn } from "@/styles/cn";
import { Button } from "@components/button";
import { Calendar } from "@components/calendar/calendar";
import {
  useFilterStore,
  usePendingDateRange,
} from "@components/filter-panel/stores/filter-store";
import { UnifiedTimeInput } from "@components/time-picker";
import { format } from "date-fns";

export const DateFilter = () => {
  const setPendingDateRange = useFilterStore(
    (state) => state.setPendingDateRange
  );
  const { selectedDate, timeFrom, timeTo } = usePendingDateRange();

  const handleDateSelect = (date: Date | undefined) => {
    setPendingDateRange({ selectedDate: date });
  };

  const handleTimeChange = (from: string, to: string) => {
    setPendingDateRange({ timeFrom: from, timeTo: to });
  };

  const handleClear = () => {
    setPendingDateRange({
      selectedDate: null,
      timeFrom: "00:00",
      timeTo: "23:59",
    });
  };

  return (
    <div className="h-full flex flex-col justify-between">
      <div>
        <div className="mt-4 grid grid-cols-2 gap-4">
          <UnifiedTimeInput
            label="Desde"
            value={timeFrom || ""}
            onChange={(value: string) => handleTimeChange(value, timeTo || "")}
            className="w-24"
          />
          <UnifiedTimeInput
            label="Hasta"
            value={timeTo || format(new Date(), "HH:mm")}
            onChange={(value: string) =>
              handleTimeChange(timeFrom || "", value)
            }
            className="w-24"
          />
        </div>
        <div className="mt-4 flex justify-center">
          <Calendar
            mode="single"
            selected={selectedDate || undefined}
            onSelect={handleDateSelect}
            className="p-3"
            disabled={{
              after: new Date(),
            }}
            defaultMonth={new Date()}
          />
        </div>
      </div>
      <div className="flex-shrink-0 pt-2 flex justify-end">
        <Button variant="link" onClick={handleClear}>
          Limpiar fecha
        </Button>
      </div>
    </div>
  );
};

DateFilter.displayName = "DateFilter";

function Skeleton({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="skeleton"
      className={cn("bg-gray-200 animate-pulse rounded-md", className)}
      {...props}
    />
  );
}
export { Skeleton };
