import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { getCompanies } from "@/shared/services/api/get-companies";
import { companiesMapper } from "@/shared/services/mappers/companies-mapper";

export const useCompaniesQuery = () => {
  const result = useQuery({
    queryKey: ["companies"],
    queryFn: getCompanies(companiesMapper),
  });

  const { isPending: isLoading, data, error } = result;

  const options = useMemo(() => data ?? [], [data]);

  return { isLoading, options, error };
};