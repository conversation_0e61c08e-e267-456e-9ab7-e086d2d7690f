import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cn } from "@styles/cn";
import * as React from "react";

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn("mt-4 focus-visible:outline-none", className)}
    {...props}
  />
));

TabsContent.displayName = "TabsContent";

export { TabsContent };
