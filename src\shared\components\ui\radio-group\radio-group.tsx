import * as RadioGroupPrimitive from "@radix-ui/react-radio-group";
import * as React from "react";

import { RadioGroupItem } from "./components/radio-group-item";
import { radioGroupStyles } from "./radio-group.styles";
import { cn } from "@styles/cn";

type RadioGroupComponentType = {
  Item: typeof RadioGroupItem;
} & React.ForwardRefExoticComponent<
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root> &
  React.RefAttributes<HTMLDivElement>
>;

const RadioGroupRoot = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>
>(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Root
      className={cn(radioGroupStyles, className)}
      {...props}
      ref={ref}
    />
  );
});

RadioGroupRoot.displayName = RadioGroupPrimitive.Root.displayName;

const RadioGroup = RadioGroupRoot as RadioGroupComponentType;

RadioGroup.Item = RadioGroupItem;

export { RadioGroup, RadioGroupItem };
