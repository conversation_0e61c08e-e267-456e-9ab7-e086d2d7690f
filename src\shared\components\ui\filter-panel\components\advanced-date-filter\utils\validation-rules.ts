import { AdvancedDateFilterState, ValidationError, CompletePeriod, DateRange } from '../types/advanced-date-filter.types';
import { isDateInFuture, doPeriodsOverlap, doDateRangesOverlap } from './date-helpers';

export const DEFAULT_MAX_PERIODS = 12;
export const DEFAULT_MAX_DATES = 31;

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings?: string[];
}

export const validateCompletePeriods = (
  periods: CompletePeriod[],
  maxPeriods: number = DEFAULT_MAX_PERIODS
): ValidationResult => {
  const errors: ValidationError[] = [];

  // Check if empty
  if (periods.length === 0) {
    errors.push({
      type: 'empty-selection',
      message: 'Debe seleccionar al menos un período completo',
      field: 'completePeriods'
    });
  }

  // Check max selection limit
  if (periods.length > maxPeriods) {
    errors.push({
      type: 'max-selection-exceeded',
      message: `No puede seleccionar más de ${maxPeriods} períodos`,
      field: 'completePeriods'
    });
  }

  // Check for future dates
  const futurePeriods = periods.filter(period => isDateInFuture(period.startDate));
  if (futurePeriods.length > 0) {
    errors.push({
      type: 'future-dates',
      message: 'No se pueden seleccionar períodos futuros',
      field: 'completePeriods'
    });
  }

  // Check for overlapping periods
  for (let i = 0; i < periods.length; i++) {
    for (let j = i + 1; j < periods.length; j++) {
      if (doPeriodsOverlap(periods[i], periods[j])) {
        errors.push({
          type: 'overlapping-periods',
          message: 'Los períodos seleccionados no pueden superponerse',
          field: 'completePeriods'
        });
        break;
      }
    }
    if (errors.some(e => e.type === 'overlapping-periods')) break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateFreeSelection = (
  selectedDates: Date[],
  dateRanges: DateRange[],
  maxDates: number = DEFAULT_MAX_DATES
): ValidationResult => {
  const errors: ValidationError[] = [];

  // Check if empty
  if (selectedDates.length === 0 && dateRanges.length === 0) {
    errors.push({
      type: 'empty-selection',
      message: 'Debe seleccionar al menos una fecha o rango',
      field: 'freeSelection'
    });
  }

  // Check max dates limit
  const totalDates = selectedDates.length + dateRanges.reduce((acc, range) => {
    const days = Math.ceil((range.endDate.getTime() - range.startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    return acc + days;
  }, 0);

  if (totalDates > maxDates) {
    errors.push({
      type: 'max-selection-exceeded',
      message: `No puede seleccionar más de ${maxDates} días en total`,
      field: 'freeSelection'
    });
  }

  // Check for future dates in specific dates
  const futureDates = selectedDates.filter(date => isDateInFuture(date));
  if (futureDates.length > 0) {
    errors.push({
      type: 'future-dates',
      message: 'No se pueden seleccionar fechas futuras',
      field: 'selectedDates'
    });
  }

  // Check for future dates in ranges
  const futureRanges = dateRanges.filter(range => 
    isDateInFuture(range.startDate) || isDateInFuture(range.endDate)
  );
  if (futureRanges.length > 0) {
    errors.push({
      type: 'future-dates',
      message: 'No se pueden seleccionar rangos con fechas futuras',
      field: 'dateRanges'
    });
  }

  // Check for invalid ranges
  const invalidRanges = dateRanges.filter(range => range.startDate > range.endDate);
  if (invalidRanges.length > 0) {
    errors.push({
      type: 'invalid-range',
      message: 'La fecha de inicio debe ser anterior a la fecha de fin',
      field: 'dateRanges'
    });
  }

  // Check for overlapping ranges
  for (let i = 0; i < dateRanges.length; i++) {
    for (let j = i + 1; j < dateRanges.length; j++) {
      if (doDateRangesOverlap(dateRanges[i], dateRanges[j])) {
        errors.push({
          type: 'overlapping-periods',
          message: 'Los rangos de fechas no pueden superponerse',
          field: 'dateRanges'
        });
        break;
      }
    }
    if (errors.some(e => e.type === 'overlapping-periods')) break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateAdvancedDateFilter = (
  state: AdvancedDateFilterState,
  config?: { maxPeriods?: number; maxDates?: number }
): ValidationResult => {
  const maxPeriods = config?.maxPeriods || DEFAULT_MAX_PERIODS;
  const maxDates = config?.maxDates || DEFAULT_MAX_DATES;

  if (state.mode === 'complete-periods') {
    return validateCompletePeriods(state.completePeriods, maxPeriods);
  } else {
    return validateFreeSelection(
      state.freeSelection.selectedDates,
      state.freeSelection.dateRanges,
      maxDates
    );
  }
};

export const getValidationMessage = (error: ValidationError): string => {
  switch (error.type) {
    case 'empty-selection':
      return error.message;
    case 'future-dates':
      return 'Las fechas futuras no están permitidas. Seleccione fechas hasta hoy.';
    case 'overlapping-periods':
      return 'Los períodos seleccionados se superponen. Ajuste su selección.';
    case 'invalid-range':
      return 'Rango de fechas inválido. La fecha de inicio debe ser anterior a la de fin.';
    case 'max-selection-exceeded':
      return error.message;
    default:
      return 'Error de validación desconocido.';
  }
};

export const getRecoveryActions = (errors: ValidationError[]): string[] => {
  const actions: string[] = [];

  if (errors.some(e => e.type === 'future-dates')) {
    actions.push('Elimine las fechas futuras de su selección');
  }

  if (errors.some(e => e.type === 'overlapping-periods')) {
    actions.push('Revise y ajuste los períodos que se superponen');
  }

  if (errors.some(e => e.type === 'invalid-range')) {
    actions.push('Corrija los rangos de fechas inválidos');
  }

  if (errors.some(e => e.type === 'max-selection-exceeded')) {
    actions.push('Reduzca el número de períodos o fechas seleccionadas');
  }

  if (errors.some(e => e.type === 'empty-selection')) {
    actions.push('Seleccione al menos un período o fecha');
  }

  return actions;
};