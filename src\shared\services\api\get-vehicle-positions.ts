import { legacyAxiosInstance } from "@services/axios";
import { get } from "./common";

export interface VehiclePositionResponse {
  date: string;
  lat: number;
  lon: number;
  speed: number;
  course: number;
  engineOn: boolean;
  receivedAt: string;
}

export const getVehiclePositions =
  <T>(mapper: (response: VehiclePositionResponse[]) => T, token?: string) =>
  async (
    vehicleId: number | null,
    fromDate: string | undefined,
    toDate: string | undefined
  ) => {
    if (!vehicleId || !fromDate || !toDate) return [];

    return mapper(
      await get<
        VehiclePositionResponse[]
      >({
        axios: legacyAxiosInstance,
        url: `/api/core/positions/${vehicleId}`,
        params: {
          fromDate,
          toDate,
        },
        token,
      })
    );
  };
