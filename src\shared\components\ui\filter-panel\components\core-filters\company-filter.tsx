import { DropdownType } from "@components/dropdown";
import { VirtualizedRadioGroup } from "@components/radio-group";
import { Input } from "@components/input";
import { SearchIcon } from "lucide-react";
import {
  useFilterStore,
  usePendingCompany,
} from "@components/filter-panel/stores/filter-store";
import { useFilterableOptions } from "@components/filter-panel/hooks/use-filterable-options";

interface CompanyFilterProps {
  loading: boolean;
  options: DropdownType[];
  moveSelectedToTop?: boolean;
}

export const CompanyFilter = ({
  options,
  moveSelectedToTop = false,
}: CompanyFilterProps) => {
  const setPendingCompany = useFilterStore((state) => state.setPendingCompany);
  const pendingCompany = usePendingCompany();

  const { filteredOptions, searchProps } = useFilterableOptions({
    options,
    selectedValue: pendingCompany,
    moveSelectedToTop,
  });

  const handleRadioChange = (value: string) => setPendingCompany(value);

  const virtualizedItems = filteredOptions.map((option) => ({
    value: option.id.toString(),
    label: option.text,
  }));

  return (
    <div className="h-full flex flex-col">
      <div className="mb-3 flex-shrink-0">
        <div className="relative">
          <SearchIcon className="absolute left-2 top-1/2 -translate-y-1/2 text-neutral-600 size-4" />
          <Input
            placeholder="Buscar distrito"
            className="pl-8"
            {...searchProps}
          />
        </div>
      </div>

      {virtualizedItems.length === 0 ? (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          No se encontraron distritos
        </div>
      ) : (
        <VirtualizedRadioGroup
          value={pendingCompany || ""}
          onValueChange={handleRadioChange}
          items={virtualizedItems}
          containerHeight="calc(100% - 60px)"
          className="flex-1"
        />
      )}
    </div>
  );
};

CompanyFilter.displayName = "CompanyFilter";
