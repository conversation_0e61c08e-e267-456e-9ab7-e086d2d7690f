interface DynamicUnsupportedFilterProps {
  type: string;
}

export const DynamicUnsupportedFilter = ({
  type,
}: DynamicUnsupportedFilterProps) => {
  return (
    <div className="h-full overflow-auto p-4">
      <div className="text-sm text-gray-500">
        Tipo de filtro "{type}" no implementado aún
        <br />
        <small>Tipos disponibles: select (radio), multiselect (checkbox)</small>
      </div>
    </div>
  );
};

DynamicUnsupportedFilter.displayName = "DynamicUnsupportedFilter";
