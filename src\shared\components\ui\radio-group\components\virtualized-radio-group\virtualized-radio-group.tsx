import { useVirtualizer } from "@tanstack/react-virtual";
import { useRef } from "react";
import { RadioGroup } from "../../radio-group";
import type { RadioGroupItemProps } from "../radio-group-item/radio-group-item";

interface VirtualizedRadioGroupProps {
  value: string;
  onValueChange: (value: string) => void;
  items: Array<{
    value: string;
    label: string;
  }>;
  estimatedItemSize?: number;
  overscan?: number;
  className?: string;
  containerHeight?: string;
  itemVariant?: RadioGroupItemProps["variant"];
}

export const VirtualizedRadioGroup = ({
  value,
  onValueChange,
  items,
  estimatedItemSize = 44,
  overscan = 5,
  className,
  containerHeight = "100%",
  itemVariant = "hover",
}: VirtualizedRadioGroupProps) => {
  const parentRef = useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: items.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => estimatedItemSize,
    overscan,
  });

  return (
    <div
      ref={parentRef}
      className={`overflow-auto ${className || ""}`}
      style={{ height: containerHeight }}
    >
      <RadioGroup value={value} onValueChange={onValueChange}>
        <div
          style={{
            height: `${virtualizer.getTotalSize()}px`,
            width: "100%",
            position: "relative",
          }}
        >
          {virtualizer.getVirtualItems().map((virtualItem) => {
            const item = items[virtualItem.index];
            return (
              <div
                key={virtualItem.key}
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "100%",
                  height: `${virtualItem.size}px`,
                  transform: `translateY(${virtualItem.start}px)`,
                }}
              >
                <RadioGroup.Item
                  value={item.value}
                  label={item.label}
                  variant={itemVariant}
                />
              </div>
            );
          })}
        </div>
      </RadioGroup>
    </div>
  );
};
