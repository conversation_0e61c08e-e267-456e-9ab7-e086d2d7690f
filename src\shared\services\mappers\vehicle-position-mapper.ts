import { VehiclePositionResponse } from "@services/api/get-vehicle-positions";

export interface RouteCoordinate {
  lat: number;
  lng: number;
  timestamp: string;
  speed: number;
}

export const vehiclePositionMapper = (
  response: VehiclePositionResponse[]
): VehiclePositionResponse[] => {
  return response.map((position) => ({
    ...position,
  }));
};

export const vehiclePositionsToRouteCoordinatesMapper = (
  positions: VehiclePositionResponse[]
): [number, number][] => {
  return positions
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .map((position) => [position.lat, position.lon]);
};
