import { useQuery } from "@tanstack/react-query";
import { DEFAULT_REFETCH_INTERVAL } from "@/shared/constants/default";
import { getF14Report, getF34Report } from "@/shared/services/api/get-reports";
import { F14ReportTableData, F34ReportTableData } from "@/shared/services/mappers/reports-mapper";

type UseReportsQueryProps = {
  fromDate: string | null;
  toDate: string | null;
  token: string;
  reportType: 'F14' | 'F34';
};

export const useReportsQuery = ({
  fromDate,
  toDate,
  token,
  reportType,
}: UseReportsQueryProps) => {
  return useQuery<F14ReportTableData[] | F34ReportTableData[]>({
    queryKey: ["reports", reportType, fromDate, toDate],
    queryFn: async () => {
      if (!fromDate || !toDate) {
        throw new Error("Date range is required");
      }

      const params = { fromDate, toDate };
      
      if (reportType === 'F14') {
        return await getF14Report(params, token)();
      } else {
        return await getF34Report(params, token)();
      }
    },
    enabled: !!fromDate && !!toDate && !!token,
    refetchInterval: DEFAULT_REFETCH_INTERVAL,
  });
};

// Specific hooks for each report type
export const useF14ReportsQuery = (
  fromDate: string | null,
  toDate: string | null,
  token: string
) => {
  return useQuery<F14ReportTableData[]>({
    queryKey: ["reports", "F14", fromDate, toDate],
    queryFn: () => {
      if (!fromDate || !toDate) {
        throw new Error("Date range is required for F14 report");
      }
      return getF14Report({ fromDate, toDate }, token)();
    },
    enabled: !!fromDate && !!toDate && !!token,
    refetchInterval: DEFAULT_REFETCH_INTERVAL,
  });
};

export const useF34ReportsQuery = (
  fromDate: string | null,
  toDate: string | null,
  token: string
) => {
  return useQuery<F34ReportTableData[]>({
    queryKey: ["reports", "F34", fromDate, toDate],
    queryFn: () => {
      if (!fromDate || !toDate) {
        throw new Error("Date range is required for F34 report");
      }
      return getF34Report({ fromDate, toDate }, token)();
    },
    enabled: !!fromDate && !!toDate && !!token,
    refetchInterval: DEFAULT_REFETCH_INTERVAL,
  });
};