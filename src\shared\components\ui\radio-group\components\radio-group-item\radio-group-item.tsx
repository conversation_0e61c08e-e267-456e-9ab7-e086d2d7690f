import * as RadioGroupPrimitive from "@radix-ui/react-radio-group";
import type { VariantProps } from "class-variance-authority";
import { Circle } from "lucide-react";
import * as React from "react";

import {
  radioGroupCircleStyles,
  radioGroupContainerStyles,
  radioGroupIndicatorStyles,
  radioGroupItemStyles,
  radioGroupLabelStyles,
} from "./radio-group-item.styles";
import { cn } from "@styles/cn";

export type LabelPosition = "right" | "left";

export type RadioGroupItemProps = React.ComponentPropsWithoutRef<
  typeof RadioGroupPrimitive.Item
> & {
  label?: React.ReactNode;
  labelPosition?: LabelPosition;
  variant?: VariantProps<typeof radioGroupContainerStyles>["variant"];
};

const RadioGroupItem = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Item>,
  RadioGroupItemProps
>(({ className, label, labelPosition, id, ...props }, ref) => {
  const uniqueId = React.useId();
  const radioId = id || uniqueId;
  const state = props.disabled ? "disabled" : "default";

  const radioButton = (
    <RadioGroupPrimitive.Item
      ref={ref}
      className={cn(radioGroupItemStyles, className)}
      id={radioId}
      {...props}
    >
      <RadioGroupPrimitive.Indicator className={radioGroupIndicatorStyles}>
        <Circle className={cn(radioGroupCircleStyles({ state }))} />
      </RadioGroupPrimitive.Indicator>
    </RadioGroupPrimitive.Item>
  );

  if (!label) {
    return radioButton;
  }

  const labelElement = (
    <label htmlFor={radioId} className={cn(radioGroupLabelStyles({ state }))}>
      {label}
    </label>
  );

  return (
    <div
      className={cn(
        radioGroupContainerStyles({ labelPosition, variant: props.variant }),
      )}
    >
      {radioButton}
      {labelElement}
    </div>
  );
});

RadioGroupItem.displayName = "RadioGroupItem";

export { RadioGroupItem };
