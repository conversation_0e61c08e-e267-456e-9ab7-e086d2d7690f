{"name": "mercury-mf-reporte-integracion-pesajes", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite --host --port 3000", "build": "tsc && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:watch": "vitest watch"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.85.0", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "@testing-library/react": "^16.0.1", "@urbetrack/urbix": "^0.3.7", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lucide-react": "^0.539.0", "react": "^19.1.1", "react-day-picker": "^9.8.1", "react-dom": "^19.1.1", "react-error-boundary": "^6.0.0", "react-hook-form": "7.58.1", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0-rc.2", "react-router": "^7.8.0", "react-toastify": "^11.0.5", "react-player": "^2.16.1", "recharts": "^2.15.0", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "vite-plugin-package-version": "^1.1.0", "xstate": "^5.19.2", "zustand": "^5.0.7"}, "devDependencies": {"@chromatic-com/storybook": "1.9.0", "@eslint/compat": "^1.3.2", "@eslint/js": "^9.33.0", "@storybook/addon-essentials": "8.3.6", "@storybook/addon-interactions": "8.3.6", "@storybook/addon-links": "8.3.6", "@storybook/addon-onboarding": "8.3.6", "@storybook/blocks": "8.3.6", "@storybook/react": "8.3.6", "@storybook/react-vite": "8.3.6", "@storybook/test": "8.3.6", "@testing-library/jest-dom": "^6.6.2", "@testing-library/user-event": "^14.5.2", "@types/leaflet": "^1.9.18", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.3.3", "@vitest/coverage-v8": "^2.1.4", "autoprefixer": "^10.4.20", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-storybook": "^0.10.1", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier-plugin-tailwindcss": "^0.6.14", "storybook": "8.3.6", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "~5.6.2", "typescript-eslint": "^8.10.0", "vite": "^5.4.9", "vite-plugin-mkcert": "^1.17.8", "vitest": "^2.1.4"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}