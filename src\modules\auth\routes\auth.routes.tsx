import { RouteErrorBoundary } from "@/shared/components";
import { RouteObject } from "react-router";
import LoginView from "../views/login.view";

export const AUTH_ROUTES = {
  ROOT: "/auth",
} as const;

export const authRoutes: RouteObject[] = [
    {
        path: AUTH_ROUTES.ROOT,
        errorElement: <RouteErrorBoundary />,
        children: [
            {
                index: true,
                element: <LoginView />,
                errorElement: <RouteErrorBoundary />,
            },
            {
                path: "login",
                element: <LoginView />,
                errorElement: <RouteErrorBoundary />,
            },
        ],
    },
];