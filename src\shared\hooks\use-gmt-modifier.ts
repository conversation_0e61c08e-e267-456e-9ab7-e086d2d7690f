import { useMemo } from "react";
import { useCompaniesQuery } from "@/shared/hooks/use-companies-query";
import { formatGmtModifier } from "../utils/format-gmt-modifier";

export const useGmtModifier = (appliedCompany: string) => {
  const { options: companiesOptions } = useCompaniesQuery();

  return useMemo(() => {
    const selectedCompany = companiesOptions.find(
      (company) => company.key === appliedCompany
    );
    if (!selectedCompany) return "00:00";

    return formatGmtModifier(selectedCompany.extra?.gmtModifier);
  }, [companiesOptions, appliedCompany]);
};