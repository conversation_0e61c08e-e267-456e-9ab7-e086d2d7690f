import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Home, Mail, Settings, User } from "lucide-react";

import { Tabs } from "./tabs";

const meta = {
  title: "Atoms/Tabs",
  component: Tabs,
  tags: ["autodocs", "components"],
  parameters: {
    docs: {
      description: {
        component:
          "The Tabs component allows users to navigate between related sections of content, displaying one section at a time.",
      },
    },
    actions: {
      argTypesRegex: "^on[A-Z].*",
    },
  },
  argTypes: {
    defaultValue: {
      description:
        "The value of the tab that should be active when initially rendered",
      control: "text",
      table: {
        type: { summary: "string" },
      },
    },
    items: {
      description: "Array of tab items to display",
      control: "object",
      table: {
        type: { summary: "TabItem[]" },
      },
    },
    size: {
      description: "Size of the tabs",
      control: "select",
      options: ["sm", "md", "lg"],
      table: {
        defaultValue: { summary: "md" },
        type: { summary: "string" },
      },
    },
    className: {
      description: "Additional CSS classes to apply to the tabs container",
      control: "text",
      table: {
        type: { summary: "string" },
      },
    },
    onChange: {
      description: "Callback function called when the active tab changes",
      action: "changed",
      table: {
        type: { summary: "function" },
      },
    },
  },
} satisfies Meta<typeof Tabs>;

export default meta;
type Story = StoryObj<typeof Tabs>;

function getTabContent(index: number) {
  return (
    <div className="p-4">
      Simple content for Tab
      {index}
    </div>
  );
}

export const Small: Story = {
  args: {
    size: "sm",
    items: [
      { value: "tab1", label: "Tab 1", content: getTabContent(1) },
      { value: "tab2", label: "Tab 2", content: getTabContent(2) },
      { value: "tab3", label: "Tab 3", content: getTabContent(3) },
    ],
    defaultValue: "tab1",
  },
};

export const Medium: Story = {
  args: {
    size: "md",
    items: [
      { value: "tab1", label: "Tab 1", content: getTabContent(1) },
      { value: "tab2", label: "Tab 2", content: getTabContent(2) },
      { value: "tab3", label: "Tab 3", content: getTabContent(3) },
    ],
    defaultValue: "tab1",
  },
};

export const Large: Story = {
  args: {
    size: "lg",
    items: [
      { value: "tab1", label: "Tab 1", content: getTabContent(1) },
      { value: "tab2", label: "Tab 2", content: getTabContent(2) },
      { value: "tab3", label: "Tab 3", content: getTabContent(3) },
    ],
    defaultValue: "tab1",
  },
};

export const SmallWithIcons: Story = {
  args: {
    size: "sm",
    items: [
      {
        value: "home",
        label: "Home",
        icon: <Home className="mr-2 h-3 w-3" />,
        content: getTabContent(1),
      },
      {
        value: "profile",
        label: "Profile",
        icon: <User className="mr-2 h-3 w-3" />,
        content: getTabContent(2),
      },
      {
        value: "settings",
        label: "Settings",
        icon: <Settings className="mr-2 h-3 w-3" />,
        content: getTabContent(3),
      },
    ],
    defaultValue: "home",
  },
};

export const MediumWithIcons: Story = {
  args: {
    size: "md",
    items: [
      {
        value: "home",
        label: "Home",
        icon: <Home className="mr-2 h-4 w-4" />,
        content: getTabContent(1),
      },
      {
        value: "profile",
        label: "Profile",
        icon: <User className="mr-2 h-4 w-4" />,
        content: getTabContent(2),
      },
      {
        value: "settings",
        label: "Settings",
        icon: <Settings className="mr-2 h-4 w-4" />,
        content: getTabContent(3),
      },
    ],
    defaultValue: "home",
  },
};

export const LargeWithIcons: Story = {
  args: {
    size: "lg",
    items: [
      {
        value: "home",
        label: "Home",
        icon: <Home className="mr-2 h-5 w-5" />,
        content: getTabContent(1),
      },
      {
        value: "profile",
        label: "Profile",
        icon: <User className="mr-2 h-5 w-5" />,
        content: getTabContent(2),
      },
      {
        value: "settings",
        label: "Settings",
        icon: <Settings className="mr-2 h-5 w-5" />,
        content: getTabContent(3),
      },
    ],
    defaultValue: "home",
  },
};

export const WithDisabledTabs: Story = {
  args: {
    size: "md",
    items: [
      { value: "tab1", label: "Active Tab", content: getTabContent(1) },
      {
        value: "tab2",
        label: "Disabled Tab",
        content: getTabContent(2),
        disabled: true,
      },
      { value: "tab3", label: "Another Tab", content: getTabContent(3) },
    ],
    defaultValue: "tab1",
  },
};

export const WithIconsAndDisabled: Story = {
  args: {
    size: "md",
    items: [
      {
        value: "home",
        label: "Home",
        icon: <Home className="mr-2 h-4 w-4" />,
        content: getTabContent(1),
      },
      {
        value: "profile",
        label: "Profile",
        icon: <User className="mr-2 h-4 w-4" />,
        content: getTabContent(2),
        disabled: true,
      },
      {
        value: "mail",
        label: "Mail",
        icon: <Mail className="mr-2 h-4 w-4" />,
        content: getTabContent(3),
      },
      {
        value: "settings",
        label: "Settings",
        icon: <Settings className="mr-2 h-4 w-4" />,
        content: getTabContent(4),
        disabled: true,
      },
    ],
    defaultValue: "home",
  },
};

export const AllSizeVariations: Story = {
  render: () => {
    return (
      <div className="flex flex-col space-y-8">
        <div>
          <h3 className="mb-2 text-sm font-medium text-gray-500">Small (sm)</h3>
          <Tabs
            size="sm"
            items={[
              { value: "tab1", label: "Tab 1", content: getTabContent(1) },
              { value: "tab2", label: "Tab 2", content: getTabContent(2) },
              { value: "tab3", label: "Tab 3", content: getTabContent(3) },
            ]}
            defaultValue="tab1"
          />
        </div>

        <div>
          <h3 className="mb-2 text-sm font-medium text-gray-500">
            Medium (md) - Default
          </h3>
          <Tabs
            size="md"
            items={[
              { value: "tab1", label: "Tab 1", content: getTabContent(1) },
              { value: "tab2", label: "Tab 2", content: getTabContent(2) },
              { value: "tab3", label: "Tab 3", content: getTabContent(3) },
            ]}
            defaultValue="tab1"
          />
        </div>

        <div>
          <h3 className="mb-2 text-sm font-medium text-gray-500">Large (lg)</h3>
          <Tabs
            size="lg"
            items={[
              { value: "tab1", label: "Tab 1", content: getTabContent(1) },
              { value: "tab2", label: "Tab 2", content: getTabContent(2) },
              { value: "tab3", label: "Tab 3", content: getTabContent(3) },
            ]}
            defaultValue="tab1"
          />
        </div>
      </div>
    );
  },
};

export const AllVariations: Story = {
  render: () => {
    return (
      <div className="flex flex-col space-y-8">
        <div>
          <h3 className="mb-2 text-sm font-medium text-gray-500">Text Only</h3>
          <Tabs
            size="md"
            items={[
              { value: "tab1", label: "Active Tab", content: getTabContent(1) },
              {
                value: "tab2",
                label: "Another Tab",
                content: getTabContent(2),
              },
              {
                value: "tab3",
                label: "Disabled Tab",
                content: getTabContent(3),
                disabled: true,
              },
            ]}
            defaultValue="tab1"
          />
        </div>

        <div>
          <h3 className="mb-2 text-sm font-medium text-gray-500">With Icons</h3>
          <Tabs
            size="md"
            items={[
              {
                value: "home",
                label: "Home",
                icon: <Home className="mr-2 h-4 w-4" />,
                content: getTabContent(1),
              },
              {
                value: "profile",
                label: "Profile",
                icon: <User className="mr-2 h-4 w-4" />,
                content: getTabContent(2),
              },
              {
                value: "settings",
                label: "Settings",
                icon: <Settings className="mr-2 h-4 w-4" />,
                content: getTabContent(3),
                disabled: true,
              },
            ]}
            defaultValue="home"
          />
        </div>
      </div>
    );
  },
};
