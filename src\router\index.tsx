import { createHashRouter, Navigate, RouteObject } from "react-router";
import { RootLayout } from "@/layout/root-layout";
import { homeRoutes } from "@/modules/home/<USER>/home.routes";
import { RouteErrorBoundary } from "@/shared/components";
import { authRoutes } from "@/modules/auth/routes/auth.routes";
import { AuthProvider } from "@/shared/providers/auth-provider";

const publicRoutes: RouteObject[] = [
  ...authRoutes,
];

const protectedRoutes: RouteObject[] = [
  {
    path: "/",
    element: <AuthProvider>
              <RootLayout />
             </AuthProvider>,
    errorElement: <RouteErrorBoundary />,
    children: [
      {
        index: true,
        element: <Navigate replace to="/home" />,
      },
      {
        path: "/home",
        children: homeRoutes,
      },
    ],
  },
  {
    path: "*",
    element: (
      <div className="flex min-h-[60vh] flex-col items-center justify-center text-center">
        <h1 className="mb-2 text-3xl font-bold text-gray-600">
          404 - Página no encontrada
        </h1>
        <p className="text-lg text-gray-600">La página que buscas no existe.</p>
      </div>
    ),
  },
];

export const router = createHashRouter([
  ...publicRoutes,
  ...protectedRoutes,
]);
