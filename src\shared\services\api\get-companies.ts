import { get } from "./common";
import { legacyAxiosInstance } from "../axios";

export type CompaniesResponse = {
  code: string;
  fantasyName: string;
  gmtModifier: number;
  id: number;
  identifiesDrivers: boolean;
  name: string;
  timeZoneId: string;
  vehiclesPerDistrict: boolean;
};

export const getCompanies =
  <TMappedOutput>(mapper: (response: CompaniesResponse[]) => TMappedOutput) =>
  async (): Promise<TMappedOutput> => {
    return mapper(
      await get<CompaniesResponse[]>({
        axios: legacyAxiosInstance,
        url: "/companies",
        withCredentials: true,
      })
    );
  };
