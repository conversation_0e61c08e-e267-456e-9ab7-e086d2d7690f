import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it } from "vitest";

import { Tabs } from "../tabs";

describe("tabs", () => {
  const mockItems = [
    {
      value: "tab1",
      label: "Tab 1",
      content: <div>Content for Tab 1</div>,
    },
    {
      value: "tab2",
      label: "Tab 2",
      content: <div>Content for Tab 2</div>,
    },
    {
      value: "tab3",
      label: "Tab 3",
      content: <div>Content for Tab 3</div>,
    },
  ];

  it("renders correctly", () => {
    render(<Tabs items={mockItems} defaultValue="tab1" />);

    expect(screen.getByText("Tab 1")).toBeInTheDocument();
    expect(screen.getByText("Tab 2")).toBeInTheDocument();
    expect(screen.getByText("Tab 3")).toBeInTheDocument();

    expect(screen.getByText("Content for Tab 1")).toBeInTheDocument();
    expect(screen.queryByText("Content for Tab 2")).not.toBeInTheDocument();
    expect(screen.queryByText("Content for Tab 3")).not.toBeInTheDocument();
  });

  it("switches tabs when clicked", async () => {
    const user = userEvent.setup();

    render(<Tabs items={mockItems} defaultValue="tab1" />);

    expect(screen.getByText("Content for Tab 1")).toBeInTheDocument();

    const tab2Button = screen.getByRole("tab", { name: /Tab 2/i });
    await user.click(tab2Button);

    expect(screen.getByText("Content for Tab 2")).toBeInTheDocument();

    const tab3Button = screen.getByRole("tab", { name: /Tab 3/i });
    await user.click(tab3Button);

    expect(screen.getByText("Content for Tab 3")).toBeInTheDocument();
  });

  it("applies active styles to the selected tab", async () => {
    const user = userEvent.setup();

    render(<Tabs items={mockItems} defaultValue="tab1" />);

    const tab1Button = screen.getByRole("tab", { name: /Tab 1/i });
    expect(tab1Button).toHaveAttribute("data-state", "active");

    const tab2Button = screen.getByRole("tab", { name: /Tab 2/i });
    await user.click(tab2Button);

    expect(tab1Button).toHaveAttribute("data-state", "inactive");
    expect(tab2Button).toHaveAttribute("data-state", "active");
  });

  it("has proper ARIA attributes", () => {
    render(<Tabs items={mockItems} defaultValue="tab1" />);

    const tablist = screen.getByRole("tablist");
    expect(tablist).toHaveAttribute("aria-orientation", "horizontal");

    const tabs = screen.getAllByRole("tab");

    expect(tabs[0]).toHaveAttribute("aria-selected", "true");

    expect(tabs[1]).toHaveAttribute("aria-selected", "false");
    expect(tabs[2]).toHaveAttribute("aria-selected", "false");

    tabs.forEach((tab) => {
      const controlsId = tab.getAttribute("aria-controls");
      expect(controlsId).toBeTruthy();

      if (tab.getAttribute("aria-selected") === "true") {
        const panel = document.getElementById(controlsId as string);
        expect(panel).toBeInTheDocument();
        expect(panel).not.toHaveAttribute("hidden");
      }
    });
  });

  it("supports keyboard navigation", async () => {
    const user = userEvent.setup();

    render(<Tabs items={mockItems} defaultValue="tab1" />);

    const tab1Button = screen.getByRole("tab", { name: /Tab 1/i });
    tab1Button.focus();

    await user.keyboard("{ArrowRight}");

    const tab2Button = screen.getByRole("tab", { name: /Tab 2/i });
    expect(document.activeElement).toBe(tab2Button);
    await user.keyboard("{Enter}");

    expect(screen.getByText("Content for Tab 2")).toBeInTheDocument();

    await user.keyboard("{ArrowRight}");

    const tab3Button = screen.getByRole("tab", { name: /Tab 3/i });
    expect(document.activeElement).toBe(tab3Button);

    await user.keyboard(" ");

    expect(screen.getByText("Content for Tab 3")).toBeInTheDocument();

    await user.keyboard("{ArrowRight}");

    expect(document.activeElement).toBe(tab1Button);
  });
});
