import { CompletePeriod, OptimizedQuery } from '../types/advanced-date-filter.types';
import { formatDateForAPI, sortPeriodsByDate } from './date-helpers';

export class QueryOptimizer {
  /**
   * Optimizes complete periods by grouping contiguous months
   */
  static optimizeCompletePeriods(periods: CompletePeriod[]): OptimizedQuery[] {
    if (periods.length === 0) return [];

    const sortedPeriods = sortPeriodsByDate(periods);
    const optimizedQueries: OptimizedQuery[] = [];
    let currentBatch: CompletePeriod[] = [];

    for (const period of sortedPeriods) {
      if (this.isContiguousPeriod(currentBatch, period)) {
        currentBatch.push(period);
      } else {
        if (currentBatch.length > 0) {
          optimizedQueries.push(this.createBatchQuery(currentBatch));
        }
        currentBatch = [period];
      }
    }

    if (currentBatch.length > 0) {
      optimizedQueries.push(this.createBatchQuery(currentBatch));
    }

    return optimizedQueries;
  }

  /**
   * Optimizes granular dates by grouping nearby dates into ranges
   */
  static optimizeGranularDates(dates: Date[]): OptimizedQuery[] {
    if (dates.length === 0) return [];

    const sortedDates = [...dates].sort((a, b) => a.getTime() - b.getTime());
    const optimizedQueries: OptimizedQuery[] = [];
    let currentRange: Date[] = [];

    for (const date of sortedDates) {
      if (this.isContiguousDate(currentRange, date)) {
        currentRange.push(date);
      } else {
        if (currentRange.length > 0) {
          optimizedQueries.push(this.createDateRangeQuery(currentRange));
        }
        currentRange = [date];
      }
    }

    if (currentRange.length > 0) {
      optimizedQueries.push(this.createDateRangeQuery(currentRange));
    }

    return optimizedQueries;
  }

  /**
   * Calculates optimization metrics
   */
  static calculateOptimizationMetrics(
    originalCount: number,
    optimizedQueries: OptimizedQuery[]
  ): {
    originalQueries: number;
    optimizedQueries: number;
    reductionPercentage: number;
    estimatedTimeSaving: number;
  } {
    const optimizedCount = optimizedQueries.length;
    const reductionPercentage = originalCount > 0 
      ? Math.round(((originalCount - optimizedCount) / originalCount) * 100)
      : 0;
    
    // Estimate time saving based on reduced API calls (assuming 500ms per call)
    const estimatedTimeSaving = (originalCount - optimizedCount) * 500;

    return {
      originalQueries: originalCount,
      optimizedQueries: optimizedCount,
      reductionPercentage,
      estimatedTimeSaving
    };
  }

  /**
   * Checks if a period is contiguous with the current batch
   */
  private static isContiguousPeriod(batch: CompletePeriod[], period: CompletePeriod): boolean {
    if (batch.length === 0) return true;

    const lastPeriod = batch[batch.length - 1];
    
    // Check if it's the next month in the same year
    if (lastPeriod.year === period.year) {
      return period.month === lastPeriod.month + 1;
    }
    
    // Check if it's January of the next year after December
    if (lastPeriod.year === period.year - 1) {
      return lastPeriod.month === 12 && period.month === 1;
    }

    return false;
  }

  /**
   * Checks if a date is contiguous with the current range (within 3 days)
   */
  private static isContiguousDate(range: Date[], date: Date): boolean {
    if (range.length === 0) return true;

    const lastDate = range[range.length - 1];
    const daysDifference = Math.abs(date.getTime() - lastDate.getTime()) / (1000 * 60 * 60 * 24);
    
    // Consider dates within 3 days as contiguous for optimization
    return daysDifference <= 3;
  }

  /**
   * Creates a batch query from contiguous periods
   */
  private static createBatchQuery(periods: CompletePeriod[]): OptimizedQuery {
    const firstPeriod = periods[0];
    const lastPeriod = periods[periods.length - 1];

    return {
      type: 'complete-period',
      startDate: formatDateForAPI(firstPeriod.startDate),
      endDate: formatDateForAPI(lastPeriod.endDate),
      periods
    };
  }

  /**
   * Creates a date range query from contiguous dates
   */
  private static createDateRangeQuery(dates: Date[]): OptimizedQuery {
    const firstDate = dates[0];
    const lastDate = dates[dates.length - 1];

    return {
      type: dates.length === 1 ? 'specific-dates' : 'date-range',
      startDate: formatDateForAPI(firstDate),
      endDate: formatDateForAPI(lastDate),
      dates: dates.map(formatDateForAPI)
    };
  }
}

/**
 * Hook for using query optimization in components
 */
export const useQueryOptimization = () => {
  const optimizePeriods = (periods: CompletePeriod[]) => {
    return QueryOptimizer.optimizeCompletePeriods(periods);
  };

  const optimizeDates = (dates: Date[]) => {
    return QueryOptimizer.optimizeGranularDates(dates);
  };

  const calculateMetrics = (originalCount: number, optimizedQueries: OptimizedQuery[]) => {
    return QueryOptimizer.calculateOptimizationMetrics(originalCount, optimizedQueries);
  };

  return {
    optimizePeriods,
    optimizeDates,
    calculateMetrics
  };
};

/**
 * Cache management for optimized queries
 */
export class QueryCache {
  private static cache = new Map<string, { data: unknown; timestamp: number; ttl: number }>();
  private static readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  static set(key: string, data: unknown, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  static get(key: string): unknown | null {
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  static clear(): void {
    this.cache.clear();
  }

  static generateKey(query: OptimizedQuery): string {
    return `${query.type}-${query.startDate}-${query.endDate}`;
  }

  static getStats(): {
    size: number;
    hitRate: number;
    memoryUsage: number;
  } {
    return {
      size: this.cache.size,
      hitRate: 0, // Would need to track hits/misses for real implementation
      memoryUsage: JSON.stringify([...this.cache.entries()]).length
    };
  }
}