import { useQuery } from "@tanstack/react-query";
import { useAuthToken } from "@/modules/auth";
import { getVehicles } from "../services/api/get-vehicles";
import { vehiclesMapper } from "../services/mappers/vehicles-mapper";

export const useVehiclesQuery = (companyId: number | null) => {
  const { token } = useAuthToken();

  const result = useQuery({
    queryKey: ["vehicles", companyId],
    queryFn: () => getVehicles(vehiclesMapper, token)(companyId!),
    enabled: !!companyId,
  });

  const { isLoading, data: vehicles, error } = result;

  return { isLoading, vehicles: vehicles ?? [], error };
};
