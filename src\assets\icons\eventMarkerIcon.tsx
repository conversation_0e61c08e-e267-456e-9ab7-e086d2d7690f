import type { SVGProps } from "react";

export const EventMarkerIcon = (
  props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>
) => (
  <svg 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx="12" cy="12" r="12" fill="#D26300"/>
    <path d="M17.5769 12.625H15.8142C15.6371 12.625 15.4887 12.5651 15.3688 12.4452C15.2491 12.3254 15.1892 12.1769 15.1892 11.9998C15.1892 11.8226 15.2491 11.6742 15.3688 11.5546C15.4887 11.4349 15.6371 11.375 15.8142 11.375H17.5769C17.754 11.375 17.9025 11.435 18.0223 11.5548C18.1421 11.6747 18.2019 11.8232 18.2019 12.0002C18.2019 12.1775 18.1421 12.3259 18.0223 12.4454C17.9025 12.5652 17.754 12.625 17.5769 12.625ZM14.1107 15.9279C14.2175 15.7816 14.3539 15.6971 14.5201 15.6746C14.6862 15.6523 14.8424 15.6945 14.9888 15.8013L16.3942 16.8573C16.5406 16.9643 16.6251 17.1008 16.6476 17.2669C16.6699 17.433 16.6277 17.5893 16.5209 17.7357C16.4141 17.8819 16.2776 17.9663 16.1115 17.9888C15.9453 18.0113 15.789 17.9691 15.6428 17.8621L14.2371 16.8061C14.0909 16.6993 14.0065 16.5628 13.984 16.3967C13.9616 16.2306 14.0039 16.0743 14.1107 15.9279ZM16.3621 7.11065L14.9567 8.1667C14.8103 8.2735 14.6541 8.31572 14.488 8.29336C14.3219 8.27086 14.1854 8.18642 14.0786 8.04003C13.9718 7.89378 13.9296 7.73753 13.9519 7.57128C13.9744 7.40517 14.0589 7.26871 14.2053 7.1619L15.6107 6.10586C15.7571 5.99906 15.9133 5.95683 16.0794 5.9792C16.2455 6.0017 16.382 6.08607 16.4888 6.23232C16.5956 6.37871 16.6378 6.53496 16.6155 6.70107C16.593 6.86732 16.5085 7.00385 16.3621 7.11065ZM8.05776 14.0834H5.71797C5.50464 14.0834 5.32575 14.0111 5.1813 13.8667C5.037 13.7224 4.96484 13.5435 4.96484 13.33V10.67C4.96484 10.4566 5.037 10.2777 5.1813 10.1334C5.32575 9.98892 5.50464 9.9167 5.71797 9.9167H8.05776L10.5513 7.42315C10.7512 7.22343 10.9812 7.17836 11.2413 7.28795C11.5014 7.39753 11.6315 7.59322 11.6315 7.87503V16.125C11.6315 16.4068 11.5014 16.6025 11.2413 16.7121C10.9812 16.8217 10.7512 16.7766 10.5513 16.5769L8.05776 14.0834ZM10.3815 9.37503L8.58984 11.1667H6.21484V12.8334H8.58984L10.3815 14.625V9.37503Z" fill="#FEEECD"/>
  </svg>
); 