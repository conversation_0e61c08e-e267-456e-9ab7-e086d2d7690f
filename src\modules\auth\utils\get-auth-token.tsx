export const getAuthToken = (): Promise<string> => {
  return new Promise((resolve) => {
    const handleMessage = (event: MessageEvent) => {
      const token = event.data.authToken;
      window.removeEventListener("message", handleMessage);
      resolve(token);
    };

    window.addEventListener("message", handleMessage);
    window.parent.postMessage({ type: "REQUEST_TOKEN" }, window.parent.location.origin);
  });
};
