import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@components/table";

export const DataTableSkeleton = ({
  rows = 10,
  cols = 6,
}: {
  rows?: number;
  cols?: number;
}) => (
  <div className="w-full bg-white animate-pulse">
    <div className="rounded-lg border">
      <Table>
        <TableHeader className="border-none bg-primary-50">
          <TableRow>
            {Array.from({ length: cols }, (_, i) => (
              <TableHead
                key={`skeleton-header-${i}`}
                className="text-[#6E4299]"
              >
                <div className="h-4 bg-gray-300 rounded w-20" />
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: rows }, (_, row) => (
            <TableRow
              key={`skeleton-row-${row}`}
              className="hover:bg-[#EBF1F3] text-dark-gray-1000"
            >
              {Array.from({ length: cols }, (_, col) => (
                <TableCell key={`skeleton-cell-${row}-${col}`}>
                  {/* Vary skeleton content to match different cell types */}
                  {col === cols - 2 ? (
                    // Status column - circular element
                    <div className="h-7 w-7 bg-gray-300 rounded" />
                  ) : col === cols - 3 ? (
                    // Link/description column - wider text
                    <div className="h-5 bg-gray-300 rounded w-32" />
                  ) : (
                    // Regular text columns
                    <div className="h-5 bg-gray-300 rounded w-20" />
                  )}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
    {/* Footer skeleton matching the pagination footer */}
    <div className="flex items-center space-x-2 whitespace-nowrap py-4 px-4 bg-neutral-50">
      <div className="flex-1">
        <div className="h-4 bg-gray-300 rounded w-32" />
      </div>
      <div className="flex items-center space-x-2">
        <div className="h-4 bg-gray-300 rounded w-20" />
        <div className="h-8 w-12 bg-gray-300 rounded" />
        <div className="h-8 w-8 bg-gray-300 rounded-full" />
        <div className="h-8 w-8 bg-gray-300 rounded-full" />
      </div>
    </div>
  </div>
);
