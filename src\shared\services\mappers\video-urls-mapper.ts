import type { MediaRequest } from "@services/api/post-media-request";

export interface VideoUrlsData {
  videoUrls: MediaRequest[];
  telemetryUrls: MediaRequest[];
}

export const videoUrlsMapper = (response: MediaRequest[]): VideoUrlsData => {
  return {
    videoUrls: response.filter((video) => video.jobReference === "DownloadStreamaxVideo"),
    telemetryUrls: response.filter((o) => o.jobReference === "DownloadStreamaxTelemetry"),
  }
};
