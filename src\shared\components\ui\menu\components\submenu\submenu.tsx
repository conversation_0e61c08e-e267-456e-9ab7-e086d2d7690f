import type { CheckedState } from "@radix-ui/react-checkbox";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useEffect } from "react";

import type { MenuItem } from "@urbetrack/urbix";
import { Accordion, AccordionContent, AccordionItem } from "@components/accordion";
import { ListItem } from "@components/list-item";

export default function Submenu({ item, selectedItems, setSelectedItems, onSelectedItemsChange, expandedItems, setExpandedItems }: {
  item: MenuItem;
  selectedItems: string[];
  setSelectedItems: (selectedItems: string[]) => void;
  onSelectedItemsChange?: (selectedItems: string[]) => void;
  setExpandedItems: (expandedItems: string[]) => void;
  expandedItems: string[];
}) {
  const onChecked = (item: MenuItem, checked: CheckedState) => {
    const items = checked
      ? [...selectedItems, item.value]
      : selectedItems.filter(selectedItem => selectedItem !== item.value);
    setSelectedItems(items);
    onSelectedItemsChange?.(items);
  };

  useEffect(() => {
    if (!item.items)
      return;
    if (selectedItems.includes(item.value) && !item.items?.map(i => i.value).some(subItem => selectedItems.includes(subItem))) {
      onChecked(item, false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedItems]);

  const getCheckedState = (item: MenuItem) => {
    if (!item.items) {
      return selectedItems.includes(item.value);
    }

    if (item.items?.map(i => i.value).every(subItem => selectedItems.includes(subItem))) {
      return true;
    }
    if (item.items?.map(i => i.value).some(subItem => selectedItems.includes(subItem))) {
      return "indeterminate";
    }
    return selectedItems.includes(item.value);
  };

  return (
    <div className="mt-2">
      <div>
        <ListItem
          key={item.value}
          rightIcon={item.items && item.items.length > 0 && (expandedItems.includes(item.value) ? <ChevronDown className="size-4 text-primary-800" /> : <ChevronUp className="size-4 text-primary-800" />)}
          onClick={() => {
            if (!item.items || item.items.length === 0) {
              return;
            }
            if (expandedItems.includes(item.value)) {
              setExpandedItems(expandedItems.filter(expandedItem => expandedItem !== item.value));
              return;
            }
            setExpandedItems([...expandedItems, item.value]);
          }}
          checkbox
          checkOnClick={!item.items}
          checked={getCheckedState(item)}
          onChecked={(checked) => {
            const items = checked
              ? [...selectedItems, item.value, ...(item.items?.map(i => i.value) || [])]
              : selectedItems.filter(selectedItem => selectedItem !== item.value).filter(selectedItem => !item.items?.map(i => i.value).includes(selectedItem));
            setSelectedItems(items);
            onSelectedItemsChange?.(items);
          }}
        >
          {item.label}
        </ListItem>
        {item.items && (
          <Accordion
            type="single"
            value={expandedItems?.includes(item.value) ? "open" : ""}
          >
            <AccordionItem value="open">
              <AccordionContent className="pl-6">
                {item.items.map(subItem => (
                  <ListItem
                    key={subItem.value}
                    checkbox
                    checkOnClick
                    checked={selectedItems.includes(subItem.value)}
                    onChecked={(checked) => {
                      const items = checked
                        ? [...selectedItems, subItem.value]
                        : selectedItems.filter(selectedItem => selectedItem !== subItem.value);

                      if (!selectedItems.includes(item.value) && checked) {
                        items.push(item.value);
                      }

                      setSelectedItems(items);
                      onSelectedItemsChange?.(items);
                    }}
                  >
                    {subItem.label}
                  </ListItem>
                ))}
              </AccordionContent>
            </AccordionItem>

          </Accordion>
        ) }

      </div>
    </div>
  );
}
