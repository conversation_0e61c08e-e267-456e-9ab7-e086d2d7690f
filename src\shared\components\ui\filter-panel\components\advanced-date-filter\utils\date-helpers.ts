import { format, startOfMonth, endOfMonth, isAfter, isBefore, isSameDay, parseISO } from 'date-fns';
import { CompletePeriod, DateRange } from '../types/advanced-date-filter.types';

export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

export const createCompletePeriod = (year: number, month: number): CompletePeriod => {
  const startDate = startOfMonth(new Date(year, month - 1));
  const endDate = endOfMonth(new Date(year, month - 1));
  
  return {
    id: generateId(),
    year,
    month,
    startDate,
    endDate
  };
};

export const formatPeriodLabel = (period: CompletePeriod): string => {
  const monthNames = [
    'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
  ];
  
  return `${monthNames[period.month - 1]} ${period.year}`;
};

export const formatDateRange = (range: DateRange): string => {
  const startStr = format(range.startDate, 'dd/MM/yyyy');
  const endStr = format(range.endDate, 'dd/MM/yyyy');
  
  if (isSameDay(range.startDate, range.endDate)) {
    return startStr;
  }
  
  return `${startStr} - ${endStr}`;
};

export const formatSpecificDates = (dates: Date[]): string => {
  if (dates.length === 0) return '';
  if (dates.length === 1) return format(dates[0], 'dd/MM/yyyy');
  
  const sortedDates = [...dates].sort((a, b) => a.getTime() - b.getTime());
  const firstDate = format(sortedDates[0], 'dd/MM/yyyy');
  const lastDate = format(sortedDates[sortedDates.length - 1], 'dd/MM/yyyy');
  
  return `${firstDate} ... ${lastDate} (${dates.length} días)`;
};

export const isDateInFuture = (date: Date): boolean => {
  const today = new Date();
  today.setHours(23, 59, 59, 999); // End of today
  return isAfter(date, today);
};

export const doPeriodsOverlap = (period1: CompletePeriod, period2: CompletePeriod): boolean => {
  return !(isAfter(period1.startDate, period2.endDate) || isBefore(period1.endDate, period2.startDate));
};

export const doDateRangesOverlap = (range1: DateRange, range2: DateRange): boolean => {
  return !(isAfter(range1.startDate, range2.endDate) || isBefore(range1.endDate, range2.startDate));
};

export const sortPeriodsByDate = (periods: CompletePeriod[]): CompletePeriod[] => {
  return [...periods].sort((a, b) => {
    if (a.year !== b.year) return a.year - b.year;
    return a.month - b.month;
  });
};

export const sortDatesByTime = (dates: Date[]): Date[] => {
  return [...dates].sort((a, b) => a.getTime() - b.getTime());
};

export const getDateRangeFromPeriods = (periods: CompletePeriod[]): { startDate: Date; endDate: Date } | null => {
  if (periods.length === 0) return null;
  
  const sortedPeriods = sortPeriodsByDate(periods);
  return {
    startDate: sortedPeriods[0].startDate,
    endDate: sortedPeriods[sortedPeriods.length - 1].endDate
  };
};

export const getDateRangeFromDates = (dates: Date[]): { startDate: Date; endDate: Date } | null => {
  if (dates.length === 0) return null;
  
  const sortedDates = sortDatesByTime(dates);
  return {
    startDate: sortedDates[0],
    endDate: sortedDates[sortedDates.length - 1]
  };
};

export const formatDateForAPI = (date: Date): string => {
  return format(date, 'yyyy-MM-dd');
};

export const parseDateFromAPI = (dateString: string): Date => {
  return parseISO(dateString);
};

export const isValidDateRange = (startDate: Date, endDate: Date): boolean => {
  return !isAfter(startDate, endDate);
};

export const getMonthsInYear = (year: number): { month: number; label: string; disabled: boolean }[] => {
  const monthNames = [
    'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
  ];
  
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;
  
  return monthNames.map((label, index) => {
    const month = index + 1;
    const disabled = year > currentYear || (year === currentYear && month > currentMonth);
    
    return {
      month,
      label,
      disabled
    };
  });
};

export const getAvailableYears = (): number[] => {
  const currentYear = new Date().getFullYear();
  const years: number[] = [];
  
  // Show last 5 years including current year
  for (let i = currentYear; i >= currentYear - 4; i--) {
    years.push(i);
  }
  
  return years;
};