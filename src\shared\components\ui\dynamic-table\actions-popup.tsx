import {
  <PERSON>d<PERSON><PERSON><PERSON>,
  MdTableRows,
  MdOutlineRemoveRedEye,
  MdOutlineSyncAlt,
} from "react-icons/md";
import { useState } from "react";

import { Button } from "../button/button";
import { Popover, PopoverContent, PopoverTrigger } from "../popover/popover";

import ButtonPopup from "./button-popup";
import ColumnsPopup from "./columns-popup";

export default function ActionsPopup({
  onColumnGroupChange,
  onVisibleColumnsChange,
  groupField,
  visibleColumns,
  columns,
}: {
  columns: { id: string; label: string }[];
  groupField: string;
  visibleColumns: string[];
  onColumnGroupChange: (column: string) => void;
  onVisibleColumnsChange: (columns: string[]) => void;
}) {
  const [actionsOpen, setActionsOpen] = useState(false);

  return (
    <Popover open={actionsOpen} onOpenChange={setActionsOpen}>
      <PopoverTrigger asChild>
        <Button className="gap-1 rounded-full p-2" variant="link">
          <MdReorder className="text-gray-600" fontSize={24} />{" "}
          <span className="text-xs">Vista</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="mt-12 flex w-[120px] flex-col items-start p-1"
        side="left"
      >
        <ColumnsPopup
          columns={columns}
          currentSelection={groupField ? [groupField] : []}
          icon={<MdTableRows className="text-gray-600" fontSize={18} />}
          multiselect={false}
          text="Agrupar"
          onSelectionChange={(columns) =>
            onColumnGroupChange(columns.length > 0 ? columns[0] : "")
          }
        />
        <ColumnsPopup
          columns={columns}
          currentSelection={visibleColumns}
          icon={
            <MdOutlineRemoveRedEye className="text-gray-600" fontSize={18} />
          }
          text="Mostrar"
          onSelectionChange={(columns) => onVisibleColumnsChange(columns)}
        />

        <ButtonPopup
          icon={
            <MdOutlineSyncAlt
              className="rotate-90 text-gray-600"
              fontSize={18}
            />
          }
          text="Ordenar"
          onClick={() => []}
        />
      </PopoverContent>
    </Popover>
  );
}
