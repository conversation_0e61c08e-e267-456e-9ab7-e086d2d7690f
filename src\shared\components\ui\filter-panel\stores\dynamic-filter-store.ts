import { create } from "zustand";
import { devtools } from "zustand/middleware";

// Generic filter value types
export type FilterValue =
  | string[]
  | string
  | Date
  | { from: Date | null; to: Date | null }
  | boolean
  | number;

export interface DynamicFilterState {
  // Pending filters (selected but not yet applied)
  pendingFilters: Record<string, FilterValue>;
  // Applied filters (active after clicking "Aplicar")
  appliedFilters: Record<string, FilterValue>;
  // Metadata about filters
  filterMetadata: Record<
    string,
    {
      type:
        | "select"
        | "multiselect"
        | "date"
        | "daterange"
        | "text"
        | "boolean"
        | "number";
      label: string;
      isActive: boolean;
    }
  >;

  // Utility actions
  setPendingFilter: (key: string, value: FilterValue) => void;
  applyPendingFilters: () => void;
  initializePendingWithApplied: () => void;
  registerFilter: (
    key: string,
    metadata: DynamicFilterState["filterMetadata"][string]
  ) => void;
  clearPendingFilter: (key: string) => void;
  clearAppliedFilter: (key: string) => void;
  clearAllPendingFilters: () => void;
  clearPendingToDefaults: () => void;
  clearAllAppliedFilters: () => void;
  clearAllFilters: () => void;
  getActiveFiltersCount: () => number;
  hasActiveFilters: () => boolean;
  hasPendingChanges: () => boolean;
  getPendingFilterValue: <T extends FilterValue>(key: string) => T | undefined;
  getAppliedFilterValue: <T extends FilterValue>(key: string) => T | undefined;
}

export const useDynamicFilterStore = create<DynamicFilterState>()(
  devtools(
    (set, get) => ({
      pendingFilters: {},
      appliedFilters: {},
      filterMetadata: {},

      setPendingFilter: (key, value) => {
        set(
          (state) => ({
            pendingFilters: { ...state.pendingFilters, [key]: value },
          }),
          false,
          `setPendingFilter:${key}`
        );
      },

      applyPendingFilters: () => {
        set(
          (state) => ({
            appliedFilters: { ...state.pendingFilters },
            filterMetadata: Object.fromEntries(
              Object.entries(state.filterMetadata).map(([key, meta]) => [
                key,
                {
                  ...meta,
                  isActive:
                    Object.prototype.hasOwnProperty.call(
                      state.pendingFilters,
                      key
                    ) &&
                    state.pendingFilters[key] !== undefined &&
                    state.pendingFilters[key] !== "" &&
                    (Array.isArray(state.pendingFilters[key])
                      ? (state.pendingFilters[key] as string[]).length > 0
                      : true),
                },
              ])
            ),
          }),
          false,
          "applyPendingFilters"
        );
      },

      initializePendingWithApplied: () => {
        set(
          (state) => ({
            pendingFilters: { ...state.appliedFilters },
          }),
          false,
          "initializePendingWithApplied"
        );
      },

      registerFilter: (key, metadata) => {
        set(
          (state) => ({
            filterMetadata: {
              ...state.filterMetadata,
              [key]: { ...metadata, isActive: false },
            },
          }),
          false,
          `registerFilter:${key}`
        );
      },

      clearPendingFilter: (key) => {
        set(
          (state) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { [key]: _, ...restFilters } = state.pendingFilters;
            return {
              pendingFilters: restFilters,
            };
          },
          false,
          `clearPendingFilter:${key}`
        );
      },

      clearAppliedFilter: (key) => {
        set(
          (state) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { [key]: _, ...restFilters } = state.appliedFilters;
            return {
              appliedFilters: restFilters,
              filterMetadata: {
                ...state.filterMetadata,
                [key]: {
                  ...state.filterMetadata[key],
                  isActive: false,
                },
              },
            };
          },
          false,
          `clearAppliedFilter:${key}`
        );
      },

      clearAllPendingFilters: () => {
        set(
          (state) => ({
            pendingFilters: { ...state.appliedFilters },
          }),
          false,
          "clearAllPendingFilters"
        );
      },

      clearPendingToDefaults: () => {
        set(
          {
            pendingFilters: {},
          },
          false,
          "clearPendingToDefaults"
        );
      },

      clearAllAppliedFilters: () => {
        set(
          (state) => ({
            appliedFilters: {},
            filterMetadata: Object.fromEntries(
              Object.entries(state.filterMetadata).map(([key, meta]) => [
                key,
                { ...meta, isActive: false },
              ])
            ),
          }),
          false,
          "clearAllAppliedFilters"
        );
      },

      clearAllFilters: () => {
        set(
          (state) => ({
            pendingFilters: {},
            appliedFilters: {},
            filterMetadata: Object.fromEntries(
              Object.entries(state.filterMetadata).map(([key, meta]) => [
                key,
                { ...meta, isActive: false },
              ])
            ),
          }),
          false,
          "clearAllFilters"
        );
      },

      getActiveFiltersCount: () => {
        const state = get();
        return Object.values(state.filterMetadata).filter(
          (meta) => meta.isActive
        ).length;
      },

      hasActiveFilters: () => {
        return get().getActiveFiltersCount() > 0;
      },

      hasPendingChanges: () => {
        const state = get();
        return (
          JSON.stringify(state.pendingFilters) !==
          JSON.stringify(state.appliedFilters)
        );
      },

      getPendingFilterValue: <T extends FilterValue>(
        key: string
      ): T | undefined => {
        return get().pendingFilters[key] as T;
      },

      getAppliedFilterValue: <T extends FilterValue>(
        key: string
      ): T | undefined => {
        return get().appliedFilters[key] as T;
      },
    }),
    {
      name: "dynamic-filter-store",
    }
  )
);

// Convenience hooks for common filter types
export const usePendingFilterValue = <T extends FilterValue>(key: string) =>
  useDynamicFilterStore((state) => state.getPendingFilterValue<T>(key));

export const useAppliedFilterValue = <T extends FilterValue>(key: string) =>
  useDynamicFilterStore((state) => state.getAppliedFilterValue<T>(key));

export const useSetPendingFilter = () =>
  useDynamicFilterStore((state) => state.setPendingFilter);

export const useApplyPendingFilters = () =>
  useDynamicFilterStore((state) => state.applyPendingFilters);

export const useInitializePendingWithApplied = () =>
  useDynamicFilterStore((state) => state.initializePendingWithApplied);

export const useRegisterFilter = () =>
  useDynamicFilterStore((state) => state.registerFilter);

export const useDynamicActiveFiltersCount = () =>
  useDynamicFilterStore((state) => state.getActiveFiltersCount());

export const useDynamicHasPendingChanges = () =>
  useDynamicFilterStore((state) => state.hasPendingChanges());

export const useClearPendingToDefaults = () =>
  useDynamicFilterStore((state) => state.clearPendingToDefaults);
