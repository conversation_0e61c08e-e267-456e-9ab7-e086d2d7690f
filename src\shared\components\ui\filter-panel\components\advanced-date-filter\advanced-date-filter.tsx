import React from 'react';
import { Button } from '@/shared/components/ui/button';
import { cn } from '@/styles/cn';
import { DateFilterModeToggle } from './components/date-filter-mode-toggle';
import { CompletePeriodSelector } from './components/complete-period-selector';
import { GranularDateSelector } from './components/granular-date-selector';
import { 
  useAdvancedDateFilterStore,
  useAdvancedDateFilterMode,
  useCompletePeriods,
  useFreeSelection,
  useAdvancedDateFilterValidation,
  useAdvancedDateFilterConfig
} from './stores/advanced-date-filter-store';
import { getValidationMessage, getRecoveryActions } from './utils/validation-rules';
import { AlertTriangle, CheckCircle, Info } from 'lucide-react';

interface AdvancedDateFilterProps {
  className?: string;
  disabled?: boolean;
}

export const AdvancedDateFilter: React.FC<AdvancedDateFilterProps> = ({
  className,
  disabled = false
}) => {
  const store = useAdvancedDateFilterStore();
  const mode = useAdvancedDateFilterMode();
  const completePeriods = useCompletePeriods();
  const freeSelection = useFreeSelection();
  const { isValid, errors } = useAdvancedDateFilterValidation();
  const config = useAdvancedDateFilterConfig();

  const hasSelections = mode === 'complete-periods' 
    ? completePeriods.length > 0
    : freeSelection.selectedDates.length > 0 || freeSelection.dateRanges.length > 0;

  const handleClearAll = () => {
    if (mode === 'complete-periods') {
      store.clearCompletePeriods();
    } else {
      store.clearFreeSelection();
    }
  };

  const handleResetToDefaults = () => {
    store.resetToDefaults();
  };

  return (
    <div className={cn("h-full flex flex-col", className)}>
      {/* Mode Toggle */}
      <div className="flex-shrink-0">
        <DateFilterModeToggle
          mode={mode}
          onModeChange={store.setMode}
          disabled={disabled}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto py-4">
        {mode === 'complete-periods' ? (
          <CompletePeriodSelector
            selectedPeriods={completePeriods}
            onAddPeriod={store.addCompletePeriod}
            onRemovePeriod={store.removeCompletePeriod}
            onClearAll={store.clearCompletePeriods}
            maxPeriods={config.maxSelectablePeriods}
            disabled={disabled}
          />
        ) : (
          <GranularDateSelector
            freeSelection={freeSelection}
            onAddSpecificDate={store.addSpecificDate}
            onRemoveSpecificDate={store.removeSpecificDate}
            onAddDateRange={store.addDateRange}
            onRemoveDateRange={store.removeDateRange}
            onSetMode={store.setFreeSelectionMode}
            onClearAll={store.clearFreeSelection}
            maxDates={config.maxSelectableDates}
            disabled={disabled}
          />
        )}
      </div>

      {/* Validation Messages */}
      {errors.length > 0 && (
        <div className="flex-shrink-0 p-3 bg-red-50 border border-red-200 rounded-lg mx-4 mb-4">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="text-sm font-medium text-red-800 mb-1">
                Errores de Validación
              </div>
              <div className="space-y-1">
                {errors.map((error, index) => (
                  <div key={index} className="text-xs text-red-700">
                    {getValidationMessage(error)}
                  </div>
                ))}
              </div>
              
              {/* Recovery actions */}
              {(() => {
                const actions = getRecoveryActions(errors);
                if (actions.length > 0) {
                  return (
                    <div className="mt-2">
                      <div className="text-xs font-medium text-red-800 mb-1">
                        Acciones sugeridas:
                      </div>
                      <ul className="text-xs text-red-700 space-y-0.5">
                        {actions.map((action, index) => (
                          <li key={index} className="flex items-start">
                            <span className="mr-1">•</span>
                            <span>{action}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  );
                }
                return null;
              })()}
            </div>
          </div>
        </div>
      )}

      {/* Success message */}
      {isValid && hasSelections && (
        <div className="flex-shrink-0 p-3 bg-green-50 border border-green-200 rounded-lg mx-4 mb-4">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <div className="text-sm text-green-800">
              Selección válida. Las fechas están listas para aplicar.
            </div>
          </div>
        </div>
      )}

      {/* Info message when no selections */}
      {!hasSelections && (
        <div className="flex-shrink-0 p-3 bg-blue-50 border border-blue-200 rounded-lg mx-4 mb-4">
          <div className="flex items-start space-x-2">
            <Info className="h-4 w-4 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-800">
              {mode === 'complete-periods' 
                ? 'Selecciona uno o más meses completos para filtrar los reportes.'
                : 'Selecciona días específicos o rangos de fechas para filtrar los reportes.'
              }
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex-shrink-0 p-4 border-t bg-gray-50">
        <div className="flex justify-between items-center">
          <div className="flex space-x-2">
            {hasSelections && (
              <Button
                variant="secondary"
                size="sm"
                onClick={handleClearAll}
                disabled={disabled}
              >
                Limpiar {mode === 'complete-periods' ? 'Períodos' : 'Fechas'}
              </Button>
            )}
            
            <Button
              variant="link"
              size="sm"
              onClick={handleResetToDefaults}
              disabled={disabled}
              className="text-gray-600"
            >
              Restablecer
            </Button>
          </div>

          {/* Selection summary */}
          <div className="text-xs text-gray-600">
            {mode === 'complete-periods' ? (
              completePeriods.length > 0 && (
                <span>
                  {completePeriods.length} período{completePeriods.length !== 1 ? 's' : ''} seleccionado{completePeriods.length !== 1 ? 's' : ''}
                </span>
              )
            ) : (
              (freeSelection.selectedDates.length > 0 || freeSelection.dateRanges.length > 0) && (
                <span>
                  {freeSelection.selectedDates.length + freeSelection.dateRanges.reduce((acc, range) => {
                    const days = Math.ceil((range.endDate.getTime() - range.startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
                    return acc + days;
                  }, 0)} día{freeSelection.selectedDates.length + freeSelection.dateRanges.length !== 1 ? 's' : ''} seleccionado{freeSelection.selectedDates.length + freeSelection.dateRanges.length !== 1 ? 's' : ''}
                </span>
              )
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

AdvancedDateFilter.displayName = 'AdvancedDateFilter';