import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { useAuthOrionQuery } from "../hooks/use-auth-orion-query";
import { useNavigate } from "react-router";

interface LoginFormData {
  user: string;
  password: string;
}

const LoginView = () => {
  const [credentials, setCredentials] = useState<LoginFormData>({ user: "", password: "" });
  const [shouldLogin, setShouldLogin] = useState(false);
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<LoginFormData>();

  const {
    data: authResponse,
    isLoading,
    error: authError,
    isError,
  } = useAuthOrionQuery({
    user: credentials.user,
    password: credentials.password,
    enabled: shouldLogin && !!credentials.user && !!credentials.password,
  });

  useEffect(() => {
    if (authResponse?.token && shouldLogin) {
      queryClient.setQueryData(["authToken"], authResponse.token);
      
      setShouldLogin(false);
      
      navigate("/");
    }
  }, [authResponse, queryClient, shouldLogin, navigate]);

  useEffect(() => {
    if (isError && authError && shouldLogin) {
      setError("root", {
        type: "manual",
        message: "Credenciales invalidas, por favor revise sus credenciales.",
      });
      setShouldLogin(false);
    }
  }, [isError, authError, setError, shouldLogin]);

  const onSubmit = (data: LoginFormData) => {
    setCredentials(data);
    setShouldLogin(true);
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="w-full max-w-md space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
            Autenticacion
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <div>
              <Label htmlFor="user">Usuario</Label>
              <Input
                id="user"
                type="text"
                autoComplete="username"
                className="mt-1"
                {...register("user", {
                  required: "Se requiere el nombre de usuario de la plataforma",
                })}
              />
              {errors.user && (
                <p className="mt-1 text-sm text-red-600">{errors.user.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="password">Contraseña</Label>
              <Input
                id="password"
                type="password"
                autoComplete="current-password"
                className="mt-1"
                {...register("password", {
                  required: "Se requiere la contraseña del usuario ingresado",
                })}
              />
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>
          </div>

          {errors.root && (
            <div className="rounded-md bg-red-50 p-4">
              <p className="text-sm text-red-800">{errors.root.message}</p>
            </div>
          )}

          <div>
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? "Ingresando..." : "Ingresar"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginView;