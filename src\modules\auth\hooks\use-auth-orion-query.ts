import { useQuery } from "@tanstack/react-query";

import { getAuthOrion, OrionLoginRequest } from "../utils/get-auth-orion";

type UseAuthOrionQueryProps = {
  user: string;
  password: string;
  enabled?: boolean;
};

export const useAuthOrionQuery = ({ user, password, enabled = true }: UseAuthOrionQueryProps) => {
  const request: OrionLoginRequest = {
    user,
    password,
  };

  return useQuery({
    queryKey: ["authToken"],
    queryFn: () => getAuthOrion(request)((response) => response)(),
    enabled: enabled && !!request.user && !!request.password,
  });
};