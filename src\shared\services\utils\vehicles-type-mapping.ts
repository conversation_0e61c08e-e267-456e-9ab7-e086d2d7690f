const VEHICLE_TYPE_MAPPINGS: Record<string, string> = {
  truck: "P<PERSON>ado",
  pickup: "<PERSON><PERSON>",
  car: "Liviano",
};

export const mapVehicleType = (vehicleType: string | undefined): string => {
  if (!vehicleType) return "N/A";

  const lowerType = vehicleType.toLowerCase();

  if (VEHICLE_TYPE_MAPPINGS[lowerType]) {
    return VEHICLE_TYPE_MAPPINGS[lowerType];
  }

  return vehicleType.charAt(0).toUpperCase() + vehicleType.slice(1).toLowerCase();
};
