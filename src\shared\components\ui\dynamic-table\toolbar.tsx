import { motion } from "framer-motion";
import { MdSearch } from "react-icons/md";
import { useState } from "react";

import { But<PERSON> } from "../button/button";
import { Input } from "../input/input";

import ActionsPopup from "./actions-popup";

export default function Toolbar({
  columns,
  groupField,
  setGroupField,
  visibleColumns,
  setVisibleColumns,
  setSearchTerm,
}: {
  columns: { id: string; label: string }[];
  groupField: string;
  setGroupField: (column: string) => void;
  visibleColumns: string[];
  setVisibleColumns: (columns: string[]) => void;
  setSearchTerm: (term: string) => void;
}) {
  const [searchOpen, setSearchOpen] = useState(false);

  return (
    <div className="w-[100vw] flex justify-between border-b border-b-[#EBEBEB] pb-2 pr-6">
      <div />
      <div>
        <div className="flex items-center gap-1">
          <Button><PERSON><PERSON><PERSON></Button>
          <Button>Eliminar</Button>
          <Button
            className="size-10 rounded-full p-2"
            variant="link"
            onClick={() => setSearchOpen(!searchOpen)}
          >
            <MdSearch className="text-gray-400" fontSize={32} />
          </Button>
          <motion.div
            animate={{
              opacity: searchOpen ? 1 : 0,
              width: searchOpen ? "20rem" : 0,
            }}
            className="overflow-hidden"
            exit={{
              opacity: 0,
              width: 0,
            }}
            initial={{
              opacity: 0,
              width: 0,
            }}
            transition={{
              duration: 0.2,
            }}
          >
            <div className="w-80">
              <Input
                className="w-full"
                placeholder="Buscar"
                onChange={(ev) => setSearchTerm(ev.target.value.toLowerCase())}
              />
            </div>
          </motion.div>
          <ActionsPopup
            columns={columns}
            groupField={groupField}
            visibleColumns={visibleColumns}
            onColumnGroupChange={setGroupField}
            onVisibleColumnsChange={setVisibleColumns}
          />
        </div>
      </div>
    </div>
  );
}
