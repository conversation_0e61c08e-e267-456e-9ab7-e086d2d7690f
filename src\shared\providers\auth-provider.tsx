import { useAuthToken } from "@/modules/auth";
import { createContext } from "react";
import { Navigate } from "react-router";

const AuthContext = createContext(null);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const auth = useAuthToken();

  if (!auth.credentials) {
    return <Navigate to="/auth/login" replace />;
  }

  return (
    <AuthContext.Provider value={auth}>
        {children}
    </AuthContext.Provider>
  );
}