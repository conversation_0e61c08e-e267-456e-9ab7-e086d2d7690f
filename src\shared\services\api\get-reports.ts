import { get } from './common';
import { mercuryServiceAxiosInstance } from '../axios/mercury-service';
import {
  F14ReportData,
  F34ReportData,
  F14ReportTableData,
  F34ReportTableData,
  ReportRequestParams,
  ReportResponse,
  f14ReportMapper,
  f34ReportMapper
} from '../mappers/reports-mapper';

// F14 Report API call
export const getF14Report = (params: Omit<ReportRequestParams, 'reportType'>, token: string) => {
  return async (): Promise<F14ReportTableData[]> => {
    const response = await get<ReportResponse<F14ReportData>>({
      axios: mercuryServiceAxiosInstance,
      url: '/Reports/Preview',
      token,
      params: {
        fromDate: params.fromDate,
        toDate: params.toDate,
        reportType: 'Format14',
      },
    });

    return f14ReportMapper(response);
  };
};

// F34 Report API call
export const getF34Report = (params: Omit<ReportRequestParams, 'reportType'>, token: string) => {
  return async (): Promise<F34ReportTableData[]> => {
    const response = await get<ReportResponse<F34ReportData>>({
      axios: mercuryServiceAxiosInstance,
      url: '/Reports/Preview',
      token,
      params: {
        fromDate: params.fromDate,
        toDate: params.toDate,
        reportType: 'Format34',
      },
    });

    return f34ReportMapper(response);
  };
};

// Generic report fetcher that can be used with different report types
export const getReportByType = (
  reportType: 'F14' | 'F34',
  params: { fromDate: string; toDate: string },
  token: string
) => {
  if (reportType === 'F14') {
    return getF14Report(params, token);
  } else {
    return getF34Report(params, token);
  }
};