import { Check, ChevronDown, ChevronUp } from "lucide-react";
import { useFormContext, useWatch } from "react-hook-form";
import { Popover, PopoverContent, PopoverTrigger } from "@components/popover";
import { Button } from "@components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@components/command";
import type { ReactNode } from "react";
import { useEffect, useState } from "react";
import { cn } from "@styles/cn";
import { Label } from "@components/label";
import { useFormRulesContext } from "@/shared/forms/form-rules";

type ComboBoxId = number | string;

export type ComboBoxOptionType<TExtra = Record<string, unknown>> = {
  id: ComboBoxId;
  text: string;
  key?: string;
  disabled?: boolean;
  selected?: boolean;
  extra?: TExtra;
};

export type ComboBoxProps<TExtra = Record<string, unknown>> = {
  name: string;
  options: ComboBoxOptionType<TExtra>[];
  placeholder?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  label?: string;
  required?: boolean;
  hasError?: boolean;
  isDisabled?: boolean;
  isReadonly?: boolean;
  isLoading?: boolean;
  hasEmptyValue?: boolean;
  className?: string;
  dynamicLeftContent?: (option: ComboBoxOptionType<TExtra>) => ReactNode;
  showErrors?: boolean;
};

export const ComboBox = ({
  name,
  options,
  placeholder = "Select option...",
  leftIcon,
  rightIcon,
  label,
  required = false,
  isDisabled = false,
  isReadonly = false,
  isLoading = false,
  hasEmptyValue = false,
  className,
  dynamicLeftContent,
  showErrors = false,
}: ComboBoxProps) => {
  const [open, setOpen] = useState(false);
  const methods = useFormContext();
  const rule = useFormRulesContext();

  const selectedValue = useWatch({
    name,
    control: methods.control,
  }) as ComboBoxId;

  const errors = showErrors ? methods.formState.errors : {};

  const isComboboxDisabled = isDisabled || isReadonly || options.length === 0;

  const selectedOption = options.find((option) => option.id === selectedValue);

  const handleSelect = (currentValue: string) => {
    if (currentValue === "") {
      methods.setValue(name, "");
      if (showErrors) {
        methods.clearErrors(name);
      }
      setOpen(false);
      return;
    }

    const selectedOption = options.find(
      (option) => option.text === currentValue
    );
    if (!selectedOption) return;

    const newValue =
      selectedOption.id === selectedValue ? "" : selectedOption.id;
    methods.setValue(name, newValue);
    if (showErrors) {
      methods.clearErrors(name);
    }
    setOpen(false);
  };

  useEffect(() => {
    methods.register(name, rule(name));
  }, [methods, name, rule]);

  return (
    <div className={cn("w-full", className)}>
      {label && (
        <Label htmlFor={name}>
          {label}
          {required && <span className="text-red-500"> *</span>}
        </Label>
      )}

      <Popover open={open} onOpenChange={setOpen} modal>
        <PopoverTrigger asChild>
          <Button
            variant="secondary"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between border hover:bg-neutral-50",
              showErrors && errors[name]
                ? "border-red-500 disabled:border-red-500"
                : "border-gray-500",
              open &&
                !errors[name] &&
                "border-primary-600 ring-2 ring-primary-600",
              isReadonly && "bg-gray-100 cursor-default",
              leftIcon && "pl-3",
              rightIcon && "pr-3"
            )}
            disabled={isComboboxDisabled}
          >
            <div className="flex items-center w-full">
              {leftIcon && <div className="mr-2 flex-shrink-0">{leftIcon}</div>}
              <div className="flex-1 text-left">
                {isLoading ? (
                  "Cargando..."
                ) : selectedOption ? (
                  selectedOption.text.length > 25 ? (
                    `${selectedOption.text.substring(0, 25)}...`
                  ) : (
                    <div className="flex items-center">
                      {dynamicLeftContent && dynamicLeftContent(selectedOption)}
                      <span className="text-gray-800">
                        {selectedOption.text.length > 25
                          ? selectedOption.text.substring(0, 25) + "..."
                          : selectedOption.text}
                      </span>
                    </div>
                  )
                ) : (
                  <span className="text-gray-800">{placeholder}</span>
                )}
              </div>
              {rightIcon && (
                <div className="ml-2 flex-shrink-0">{rightIcon}</div>
              )}
              {open ? (
                <ChevronUp
                  className={cn(
                    "ml-2 h-4 w-4 shrink-0 opacity-50",
                    showErrors && errors[name] && "text-error-500"
                  )}
                />
              ) : (
                <ChevronDown
                  className={cn(
                    "ml-2 h-4 w-4 shrink-0 opacity-50",
                    showErrors && errors[name] && "text-error-500"
                  )}
                />
              )}
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-white">
          <Command
            filter={(value, search) => {
              return value.toLowerCase().includes(search.toLowerCase()) ? 1 : 0;
            }}
          >
            <CommandInput
              placeholder={`Buscar ${placeholder.toLowerCase()}...`}
            />
            <CommandList>
              <CommandEmpty>No se encontró ningun resultado</CommandEmpty>
              <CommandGroup>
                {hasEmptyValue && (
                  <CommandItem value="" onSelect={() => handleSelect("")}>
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        !selectedValue ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {placeholder}
                  </CommandItem>
                )}
                {options.map((option) => (
                  <CommandItem
                    key={option.key ?? option.id}
                    value={option.text}
                    onSelect={handleSelect}
                    disabled={option.disabled}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedValue === option.id
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    {dynamicLeftContent && dynamicLeftContent(option)}
                    {option.text}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {showErrors && errors[name] && (
        <p className="text-error-500 text-sm font-medium mt-1">
          {errors[name]?.message as string}
        </p>
      )}
    </div>
  );
};
