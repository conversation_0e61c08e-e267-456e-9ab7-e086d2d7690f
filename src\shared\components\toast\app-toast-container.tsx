import { useToastAwarePositioning } from "./toast-aware-positioning";
import { ToastContainer } from "./toast-container";

export function AppToastContainer() {
  const toastAwarePositioning = useToastAwarePositioning();

  return (
    <ToastContainer
      positioning={toastAwarePositioning}
      transition={{
        property: "right",
        duration: "0.3s",
        timingFunction: "ease-in-out",
      }}
    />
  );
}
