import { MdOutlineAssessment, MdOutlineDescription } from "react-icons/md";

import { DataTable } from "@/shared/components/ui/data-table";
import type { F14ReportTableData, F34ReportTableData } from "@/shared/services/mappers/reports-mapper";
import type { ColumnDef } from "@tanstack/react-table";
import { useSearchParams } from "react-router";
import { SearchBar } from "@/shared/components/ui/data-table/search-bar";

type HomeViewProps = {
  tableColumnsF14Reports: ColumnDef<F14ReportTableData>[];
  tableDataF14Reports: F14ReportTableData[];
  tableColumnsF34Reports: ColumnDef<F34ReportTableData>[];
  tableDataF34Reports: F34ReportTableData[];
  isLoadingF14Reports: boolean;
  isLoadingF34Reports: boolean;
  searchF14Reports: string;
  searchF34Reports: string;
  onSearchF14ReportsChange: (value: string) => void;
  onSearchF34ReportsChange: (value: string) => void;
  onTabChange: (value: string) => void;
  hasDateRange: boolean;
};

export const HomeView = ({
  tableColumnsF14Reports,
  tableDataF14Reports,
  tableColumnsF34Reports,
  tableDataF34Reports,
  isLoadingF14Reports,
  isLoadingF34Reports,
  searchF14Reports,
  searchF34Reports,
  onSearchF14ReportsChange,
  onSearchF34ReportsChange,
  onTabChange,
  hasDateRange,
}: HomeViewProps) => {
  const [searchParams] = useSearchParams();

  const activeTab = searchParams.get("tab") || "reporte-f14";

  const handleTabChange = (value: string) => {
    onTabChange(value);
  };

  const tabItems = [
    {
      value: "reporte-f14",
      label: "Reporte F14",
      icon: <MdOutlineAssessment className="w-[24px] h-[24px]" />,
      content: (
        <DataTable
          columns={tableColumnsF14Reports}
          data={tableDataF14Reports}
          isLoading={isLoadingF14Reports}
          emptyMessage={
            !hasDateRange
              ? "Seleccione un rango de fechas en el panel de filtros para ver los reportes F14"
              : "No hay datos de reportes F14 disponibles para el rango de fechas seleccionado"
          }
        />
      ),
    },
    {
      value: "reporte-f34",
      label: "Reporte F34",
      icon: <MdOutlineDescription className="w-[24px] h-[24px]" />,
      content: (
        <DataTable
          columns={tableColumnsF34Reports}
          data={tableDataF34Reports}
          isLoading={isLoadingF34Reports}
          emptyMessage={
            !hasDateRange
              ? "Seleccione un rango de fechas en el panel de filtros para ver los reportes F34"
              : "No hay datos de reportes F34 disponibles para el rango de fechas seleccionado"
          }
        />
      ),
    },
  ];

  const getCurrentSearchValue = () => {
    return activeTab === "reporte-f14" ? searchF14Reports : searchF34Reports;
  };

  const getCurrentSearchHandler = () => {
    return activeTab === "reporte-f14"
      ? onSearchF14ReportsChange
      : onSearchF34ReportsChange;
  };

  const getSearchPlaceholder = () => {
    return activeTab === "reporte-f14"
      ? "Buscar por placa, NUAP, destino, área de reciclaje..."
      : "Buscar por placa, empresa, NIT, NUSD, origen...";
  };

  return (
    <div className="relative">
      <div className="flex items-center justify-between pb-1">
        <div className="flex-1">
          <div className="flex items-center gap-0">
            {tabItems.map((tab) => (
              <button
                key={tab.value}
                onClick={() => handleTabChange(tab.value)}
                className={`flex items-center gap-1 px-2 py-1 text-sm font-medium border-b-2 transition-colors cursor-pointer ${
                  activeTab === tab.value
                    ? "border-primary-800 text-primary-800"
                    : "border-neutral-400 text-neutral-600 hover:text-neutral-800 hover:border-neutral-300"
                }`}
              >
                <span>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>
        <div className="flex-shrink-0">
          <SearchBar
            value={getCurrentSearchValue()}
            onChange={getCurrentSearchHandler()}
            placeholder={getSearchPlaceholder()}
          />
        </div>
      </div>
      <div className="mt-4">
        {tabItems.find((tab) => tab.value === activeTab)?.content}
      </div>
    </div>
  );
};
