import axios from 'axios';

const MERCURY_BASE_URL = 'https://emvarias.azurewebsites.net/api/v1';

export const mercuryServiceAxiosInstance = axios.create({
  baseURL: MERCURY_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
mercuryServiceAxiosInstance.interceptors.request.use(
  (config) => {
    // Token will be added dynamically in the API calls
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
mercuryServiceAxiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Mercury API Error:', error);
    return Promise.reject(error);
  }
);