import {
  useFilterStore,
  useActiveFiltersCount,
  useStaticHasPendingChanges,
  usePendingCompany,
  useAppliedCompany,
} from "@components/filter-panel/stores/filter-store";
import {
  useDynamicFilterStore,
  useDynamicActiveFiltersCount,
  useApplyPendingFilters,
  useInitializePendingWithApplied,
  useDynamicHasPendingChanges,
  useClearPendingToDefaults,
} from "@components/filter-panel/stores/dynamic-filter-store";

export function useFilterStores() {
  const applyStaticFilters = useFilterStore(
    (state) => state.applyPendingFilters
  );
  const initializeStaticPending = useFilterStore(
    (state) => state.initializePendingWithApplied
  );
  const clearStaticPending = useFilterStore(
    (state) => state.clearPendingToDefaults
  );
  const staticHasPendingChanges = useStaticHasPendingChanges();
  const pendingCompany = usePendingCompany();
  const appliedCompany = useAppliedCompany();

  const clearDynamicPending = useClearPendingToDefaults();
  const applyPendingFilters = useApplyPendingFilters();
  const initializePendingWithApplied = useInitializePendingWithApplied();
  const dynamicHasPendingChanges = useDynamicHasPendingChanges();

  const appliedDynamicFilters = useDynamicFilterStore(
    (state) => state.appliedFilters
  );

  const staticActiveFiltersCount = useActiveFiltersCount();
  const dynamicActiveFiltersCount = useDynamicActiveFiltersCount();
  const activeFiltersCount =
    staticActiveFiltersCount + dynamicActiveFiltersCount;
  const hasPendingChanges = staticHasPendingChanges || dynamicHasPendingChanges;

  return {
    applyStaticFilters,
    initializeStaticPending,
    clearStaticPending,
    staticHasPendingChanges,
    pendingCompany,
    appliedCompany,
    clearDynamicPending,
    applyPendingFilters,
    initializePendingWithApplied,
    dynamicHasPendingChanges,
    appliedDynamicFilters,
    activeFiltersCount,
    hasPendingChanges,
  };
}
