import { ReactElement, type ReactNode, isValidElement } from "react";
import {
  Drawer,
  DrawerTrigger,
  Drawer<PERSON>ontent,
  DrawerHeader,
} from "../drawer/drawer-primitives";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import { MiniDrawer } from "./components/mini-drawer";
import { ButtonWithIcon } from "@components/button/button-with-icon";
import { MdTune } from "react-icons/md";
import React from "react";
import { Button } from "@components/button";
import { FilterDescription } from "./components/filter-description";
import { getFilterTitle } from "./utils/get-filter-title";
import { FilterLoadingState } from "./components/filter-loading-state";
import { FilterDisabledState } from "./components/filter-disabled-state";
import { useFilterPanel } from "./hooks/use-filter-panel";
import {
  isFilterLoading,
  isFilterDisabled,
  getDisabledReason,
} from "./utils/filter-helpers";

type FilterPanelProps = {
  children: ReactNode;
  onClearFilters?: () => void;
  onApplyFilters?: () => void;
};

export function FilterPanel({
  children,
  onClearFilters,
  onApplyFilters,
}: FilterPanelProps) {
  const {
    pendingCompany,
    appliedCompany,
    appliedDynamicFilters,
    hasPendingChanges,
    drawerRef,
    miniDrawerRef,
    isDrawerOpen,
    setIsDrawerOpen,
    selectedFilter,
    setSelectedFilter,
    activeFiltersCount,
    handleFilterClick,
    handleClearFilters,
    handleApplyFilters,
  } = useFilterPanel({ onClearFilters, onApplyFilters });

  return (
    <>
      <Drawer
        open={isDrawerOpen}
        onOpenChange={setIsDrawerOpen}
        direction="right"
      >
        <DrawerTrigger onClick={(e) => e.stopPropagation()} asChild>
          <ButtonWithIcon
            variant="link"
            icon={
              <div className="relative">
                <MdTune />
                {activeFiltersCount > 0 && (
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-warning-500 rounded-full" />
                )}
              </div>
            }
            text="Filtros"
          />
        </DrawerTrigger>
        <DrawerContent ref={drawerRef} aria-describedby="filter-panel">
          <DrawerHeader title="Filtros" />
          <ul className="flex-1 overflow-y-auto pt-4 bg-white">
            {React.Children.map(children, (child: ReactNode, index) => {
              if (!isValidElement(child))
                throw Error("El componente de filtro debe ser un ReactElement");

              const componentName =
                typeof child.type === "function" && "displayName" in child.type
                  ? (child.type.displayName as string)
                  : typeof child.type === "function" && "name" in child.type
                  ? (child.type.name as string)
                  : "Unknown";

              const dynamicLabel =
                componentName === "DynamicFilter"
                  ? child.props?.label
                  : undefined;

              const filterTitle = getFilterTitle(componentName, dynamicLabel);

              const isLoading = isFilterLoading(
                componentName,
                child,
                pendingCompany,
                appliedCompany,
                appliedDynamicFilters
              );
              const isDisabled = isFilterDisabled(
                componentName,
                child,
                pendingCompany,
                appliedDynamicFilters
              );
              const disabledReason = isDisabled
                ? getDisabledReason(componentName, child)
                : "";

              return (
                <li
                  key={index}
                  className={`flex items-center gap-2 pl-[1.5rem] p-2 rounded cursor-pointer ${
                    isLoading || isDisabled
                      ? "opacity-50 cursor-not-allowed"
                      : "hover:bg-neutral-200 hover:text-primary-800"
                  }`}
                  onClick={(e) => handleFilterClick(child as ReactElement, e)}
                >
                  {selectedFilter?.key === String(index) ? (
                    <IoIosArrowBack />
                  ) : (
                    <IoIosArrowForward />
                  )}
                  <div className="flex-1">
                    <span className="font-semibold text-dark-gray-1000">
                      {filterTitle}
                    </span>
                    {isLoading ? (
                      <FilterLoadingState />
                    ) : isDisabled ? (
                      <FilterDisabledState reason={disabledReason} />
                    ) : (
                      <FilterDescription
                        componentName={componentName}
                        child={child as ReactElement}
                        dynamicLabel={dynamicLabel}
                      />
                    )}
                  </div>
                </li>
              );
            })}
          </ul>

          {/* Action Buttons */}
          <div className="p-4 mt-auto flex justify-between items-center bg-white">
            <div className="flex-1">
              <p className="text-sm text-gray-500 font-medium flex-1 text-center">
                v{import.meta.env.PACKAGE_VERSION}
              </p>
            </div>
            <div className="flex gap-3 w-full justify-end">
              <Button onClick={handleClearFilters} variant="link">
                Limpiar todos
              </Button>
              <Button
                onClick={handleApplyFilters}
                disabled={!hasPendingChanges}
                variant="primary"
              >
                Aplicar
              </Button>
            </div>
          </div>

          <MiniDrawer
            title={
              selectedFilter &&
              (() => {
                const componentName =
                  typeof selectedFilter.type === "function" &&
                  "displayName" in selectedFilter.type
                    ? (selectedFilter.type.displayName as string)
                    : typeof selectedFilter.type === "function" &&
                      "name" in selectedFilter.type
                    ? (selectedFilter.type.name as string)
                    : "Unknown";
                const dynamicLabel =
                  componentName === "DynamicFilter"
                    ? selectedFilter.props?.label
                    : undefined;
                return getFilterTitle(componentName, dynamicLabel);
              })()
            }
            ref={miniDrawerRef}
            isOpen={!!selectedFilter && isDrawerOpen}
            onClose={() => setSelectedFilter(null)}
          >
            {selectedFilter}
          </MiniDrawer>
        </DrawerContent>
      </Drawer>
    </>
  );
}
