export interface CompletePeriod {
  year: number;
  month: number; // 1-12
  startDate: Date;
  endDate: Date;
  id: string;
}

export interface DateRange {
  startDate: Date;
  endDate: Date;
  id: string;
}

export interface FreeSelection {
  selectedDates: Date[];
  dateRanges: DateRange[];
  mode: 'specific-days' | 'date-ranges';
}

export type FilterMode = 'complete-periods' | 'free-selection';

export interface ValidationError {
  type: 'invalid-range' | 'overlapping-periods' | 'future-dates' | 'empty-selection' | 'max-selection-exceeded';
  message: string;
  field?: string;
}

export interface AdvancedDateFilterState {
  mode: FilterMode;
  completePeriods: CompletePeriod[];
  freeSelection: FreeSelection;
  isValid: boolean;
  validationErrors: ValidationError[];
}

export interface OptimizedQuery {
  type: 'complete-period' | 'date-range' | 'specific-dates';
  startDate: string;
  endDate: string;
  periods?: CompletePeriod[];
  dates?: string[];
}

export interface PersistentConfig {
  defaultMode: FilterMode;
  favoritePeriodsPresets: CompletePeriod[][];
  recentSelections: AdvancedDateFilterState[];
  showValidationHints: boolean;
  compactView: boolean;
  maxSelectablePeriods: number;
  maxSelectableDates: number;
}