import React from "react";
import { useRouteError, isRouteErrorResponse } from "react-router";
import { DefaultErrorFallback } from "./default-error-fallback";
import { handleError } from "./utils/handle-error";

export const RouteErrorBoundary: React.FC = () => {
  const error = useRouteError();

  React.useEffect(() => {
    if (error instanceof Error) {
      handleError(error, "Route Error");
    } else if (isRouteErrorResponse(error)) {
      handleError(
        new Error(`${error.status} ${error.statusText}`),
        "Route Error"
      );
    }
  }, [error]);

  if (isRouteErrorResponse(error)) {
    return (
      <div className="flex min-h-[400px] w-full flex-col items-center justify-center rounded-lg border border-red-200 bg-red-50 p-8 text-center">
        <div className="mb-4 text-6xl">🔍</div>
        <h1 className="mb-2 text-4xl font-bold text-red-800">{error.status}</h1>
        <p className="mb-4 text-xl text-red-600">{error.statusText}</p>
        {error.data && <p className="text-red-500">{error.data}</p>}
      </div>
    );
  }

  if (error instanceof Error) {
    return (
      <DefaultErrorFallback
        error={error}
        resetErrorBoundary={() => {
          window.location.reload();
        }}
      />
    );
  }

  return (
    <div className="flex min-h-[400px] w-full flex-col items-center justify-center rounded-lg border border-red-200 bg-red-50 p-8 text-center">
      <div className="mb-4 text-6xl">❌</div>
      <h2 className="mb-2 text-xl font-semibold text-red-800">
        Error desconocido
      </h2>
      <p className="text-red-600">
        Ocurrió un error inesperado en la navegación.
      </p>
    </div>
  );
};
