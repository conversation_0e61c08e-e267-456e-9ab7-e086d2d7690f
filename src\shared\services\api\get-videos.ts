import { billingServiceAxiosInstance } from "@services/axios/billing-service";
import { get } from "./common";
import { getQueryParams, QueryParams } from "@services/utils/query-params";

export interface VideosResponse {
  pageNumber: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  items: Item[];
}

export interface Item {
  id: number;
  dateTime: Date;
  exEventId: number;
  dvrDeviceId: string;
  exCompanyId: number;
  exVehicleId: number;
  description: string;
  plate: string;
  unit: string;
  eventDate: Date;
  startDate: Date;
  endDate: Date;
  downloadStatus: string;
  deviceStatus: string;
  labels: string[];
  vehicleType: string;
}

export const getVideos =
  <T>(mapper: (response: VideosResponse) => T, token?: string) =>
  async (companyId: number, params?: QueryParams, vehicleId?: number) => {
    const baseUrl = `/api/company/${companyId}`;
    let url = vehicleId
      ? `${baseUrl}/vehicles/${vehicleId}/videos`
      : `${baseUrl}/videos`;

    if (params) {
      const queryParams = getQueryParams(params);
      url += `?${queryParams}`;
    }

    return mapper(
      await get<VideosResponse>({
        axios: billingServiceAxiosInstance,
        url,
        token,
        withCredentials: false,
      })
    );
  };
