"use no memo";
import type {
  ColumnDef,
  ColumnFiltersState,
  PaginationState,
  Table as ReactTable,
  Row,
  RowSelectionState,
  SortingState,
  TableOptions,
} from "@tanstack/react-table";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Checkbox } from "@components/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@components/table";
import { cn } from "@styles/cn";
import { useEffect, useMemo, useState, useRef } from "react";
import {
  MdArrowDownward,
  MdArrowUpward,
  MdChevronLeft,
  MdChevronRight,
  MdFirstPage,
  MdLastPage,
} from "react-icons/md";
import { useSearchParams } from "react-router";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/select";
import { DataTableSkeleton } from "./skeleton";
import { SearchBar } from "./search-bar";

export type TableData<TData> = TData & { id: string };

/**
 * Ejemplos de paginacion
 *
 * @example Client-side pagination (default)
 * ```tsx
 * <DataTable
 *   data={allData}
 *   columns={columns}
 *   pagination={{ type: "client" }}
 * />
 * ```
 *
 * @example Server-side pagination
 * ```tsx
 * const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });
 *
 * <DataTable
 *   data={currentPageData}
 *   columns={columns}
 *   pagination={{
 *     type: "server",
 *     rowCount: totalItems,
 *     pagination: pagination, /// Pass current state for controlled pagination
 *     onPaginationChange: (newPagination) => {
 *       setPagination(newPagination);
 *       /// Fetch new data based on newPagination.pageIndex and newPagination.pageSize
 *     }
 *   }}
 * />
 * ```
 *
 * @example No pagination
 * ```tsx
 * <DataTable
 *   data={data}
 *   columns={columns}
 *   pagination={{ type: "none" }}
 * />
 * ```
 */
export type PaginationType =
  | { type: "client" }
  | {
      type: "server";
      rowCount: number;
      onPaginationChange: (pagination: PaginationState) => void;
      pagination?: PaginationState;
    }
  | { type: "none" };

export type DataTableProps<TData, TValue> = {
  columns: ColumnDef<TableData<TData>, TValue>[];
  data: TData[];
  pageSize?: number;
  emptyMessage?: string;
  pagination?: PaginationType;
  renderCustomFooter?: (
    table: ReactTable<TableData<TData>>,
    pagination: PaginationState
  ) => JSX.Element;
  styles?: Record<keyof typeof defaultStyles, string>;
  enableRowSelection?: boolean;
  customRowsRender?: (
    rows: Row<TableData<TData>>[],
    hasSelectColumn: boolean
  ) => JSX.Element;
  className?: string;
  tableConfig?: Partial<TableOptions<TData>>;
  onTableInit?: (table: ReactTable<TableData<TData>>) => void;
  rowClickHandler?: (row: Row<TableData<TData>>) => void;
  useUrlPagination?: boolean;
  urlParamPrefix?: string;
  isLoading?: boolean;
  enableSearch?: boolean;
  searchPlaceholder?: string;
  onSearchChange?: (value: string) => void;
  searchValue?: string;
};

const defaultStyles = {
  headerBackground: "bg-primary-50",
  headerText: "text-[#6E4299]",
  rowHover: "hover:bg-[#EBF1F3]",
  selectedRow: "bg-[#EBF1F3]",
};

const pageButtonClass =
  "flex size-8 items-center justify-center rounded-full border-none hover:bg-slate-200 disabled:opacity-50";

function TableFooter<TData>({
  table,
  pagination,
}: {
  table: ReactTable<
    TData & {
      id: string;
    }
  >;
  pagination: PaginationState;
}) {
  const pageSizeOptions = [5, 10, 15];
  const allPageSizeOptions = pageSizeOptions.includes(pagination.pageSize)
    ? pageSizeOptions
    : [...pageSizeOptions, pagination.pageSize].sort((a, b) => a - b);
  const [pageInput, setPageInput] = useState(
    (pagination.pageIndex + 1).toString()
  );

  const handlePageChange = (value: string) => {
    const pageNumber = parseInt(value, 10);
    if (
      !isNaN(pageNumber) &&
      pageNumber >= 1 &&
      pageNumber <= table.getPageCount()
    ) {
      table.setPageIndex(pageNumber - 1);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === "" || /^\d+$/.test(value)) {
      setPageInput(value);
    }
  };

  const handleNavigation = () => {
    if (pageInput === "" || isNaN(parseInt(pageInput, 10))) {
      setPageInput((pagination.pageIndex + 1).toString());
    } else {
      handlePageChange(pageInput);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") handleNavigation();
  };

  useEffect(() => {
    setPageInput((pagination.pageIndex + 1).toString());
  }, [pagination.pageIndex]);

  return (
    <div className="flex items-center space-x-2 whitespace-nowrap py-4 px-4 bg-neutral-50">
      <div className="flex-1 flex items-center gap-4">
        <div>
          {pagination.pageIndex * pagination.pageSize + 1} -{" "}
          {Math.min(
            pagination.pageIndex * pagination.pageSize + pagination.pageSize,
            table.getRowCount()
          )}{" "}
          de {table.getRowCount()} resultados
        </div>
      </div>
      <div className="flex flex-1 justify-end items-center space-x-1">
        <div className="flex items-center gap-2">
          <span className="text-sm">Filas por página:</span>
          <Select
            value={pagination.pageSize.toString()}
            onValueChange={(value) => table.setPageSize(Number(value))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Filas por página" />
            </SelectTrigger>
            <SelectContent>
              {allPageSizeOptions.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <button
          type="button"
          className={pageButtonClass}
          disabled={!table.getCanPreviousPage()}
          onClick={() => table.firstPage()}
        >
          <MdFirstPage className="size-5" />
        </button>
        <button
          type="button"
          className={pageButtonClass}
          disabled={!table.getCanPreviousPage()}
          onClick={() => table.previousPage()}
        >
          <MdChevronLeft className="size-5" />
        </button>
        <div className="flex items-center gap-2">
          <input
            className="h-10 w-10 rounded border border-slate-300 text-center [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
            type="number"
            value={pageInput}
            onChange={handleInputChange}
            onBlur={handleNavigation}
            onKeyDown={handleKeyDown}
            min={1}
            max={table.getPageCount()}
          />{" "}
          de {table.getPageCount()}
        </div>
        <button
          type="button"
          className={pageButtonClass}
          disabled={!table.getCanNextPage()}
          onClick={() => table.nextPage()}
        >
          <MdChevronRight className="size-5" />
        </button>
        <button
          type="button"
          className={pageButtonClass}
          disabled={!table.getCanNextPage()}
          onClick={() => table.lastPage()}
        >
          <MdLastPage className="size-5" />
        </button>
      </div>
    </div>
  );
}

export function DataTable<TData, TValue>({
  columns,
  data,
  pageSize = 10,
  emptyMessage,
  pagination: paginationConfig,
  styles = defaultStyles,
  enableRowSelection = false,
  tableConfig,
  className,
  useUrlPagination,
  urlParamPrefix = "",
  isLoading = false,
  renderCustomFooter,
  customRowsRender,
  onTableInit,
  rowClickHandler,
  enableSearch = false,
  searchPlaceholder = "Buscar...",
  onSearchChange,
  searchValue,
}: DataTableProps<TableData<TData>, TValue>) {
  const [searchParams, setSearchParams] = useSearchParams();

  const finalPaginationConfig = paginationConfig || { type: "client" as const };

  const getPageParamName = () =>
    urlParamPrefix ? `${urlParamPrefix}Page` : "page";
  const getPageSizeParamName = () =>
    urlParamPrefix ? `${urlParamPrefix}PageSize` : "pageSize";

  const getInitialPage = (): number => {
    if (!useUrlPagination) return 0;
    const page = parseInt(searchParams.get(getPageParamName()) || "1", 10);
    return Math.max(0, page - 1);
  };

  const getInitialPageSize = (): number => {
    if (!useUrlPagination) return pageSize;
    const urlPageSize = parseInt(
      searchParams.get(getPageSizeParamName()) || pageSize.toString(),
      10
    );
    return urlPageSize > 0 ? urlPageSize : pageSize;
  };

  const isControlledPagination =
    finalPaginationConfig.type === "server" && finalPaginationConfig.pagination;

  const [internalPaginationState, setInternalPaginationState] =
    useState<PaginationState>({
      pageIndex: getInitialPage(),
      pageSize: getInitialPageSize(),
    });

  const paginationState = isControlledPagination
    ? finalPaginationConfig.pagination!
    : internalPaginationState;

  const handlePaginationChange = (
    updater: ((prev: PaginationState) => PaginationState) | PaginationState
  ) => {
    const currentState = paginationState;
    const newPagination =
      typeof updater === "function" ? updater(currentState) : updater;

    if (useUrlPagination) {
      const newParams = new URLSearchParams(searchParams);

      if (newPagination.pageIndex !== currentState.pageIndex) {
        newParams.set(
          getPageParamName(),
          (newPagination.pageIndex + 1).toString()
        );
      }

      if (newPagination.pageSize !== currentState.pageSize) {
        newParams.set(
          getPageSizeParamName(),
          newPagination.pageSize.toString()
        );
        newParams.set(getPageParamName(), "1");
      }

      setSearchParams(newParams);
    }

    if (finalPaginationConfig.type === "server") {
      finalPaginationConfig.onPaginationChange(newPagination);
    }

    if (!isControlledPagination) {
      setInternalPaginationState(newPagination);
    }
  };

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [globalFilter, setGlobalFilter] = useState<string>(searchValue || "");
  const prevSearchValueRef = useRef(searchValue);

  if (prevSearchValueRef.current !== searchValue) {
    prevSearchValueRef.current = searchValue;
    if (searchValue !== undefined) {
      setGlobalFilter(searchValue);
    }
  }

  const handleGlobalFilterChange = (value: string) => {
    setGlobalFilter(value);
    onSearchChange?.(value);
  };

  const isServerPagination = finalPaginationConfig.type === "server";
  const isPaginationEnabled = finalPaginationConfig.type !== "none";
  const isManualFiltering = !!onSearchChange;

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel:
      isPaginationEnabled && !isServerPagination
        ? getPaginationRowModel()
        : undefined,
    onPaginationChange: handlePaginationChange,
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: !isManualFiltering ? getFilteredRowModel() : undefined,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    manualPagination: isServerPagination,
    manualFiltering: isManualFiltering,
    rowCount: isServerPagination ? finalPaginationConfig.rowCount : undefined,
    globalFilterFn: "includesString",
    ...tableConfig,
    state: {
      sorting,
      columnFilters,
      pagination: paginationState,
      rowSelection,
      globalFilter,
      ...tableConfig?.state,
    },
    getRowId: (row) => row.id,
    autoResetPageIndex: !isServerPagination,
    enableRowSelection,
  });

  const hasSelectColumn = useMemo(
    () => columns.some((column) => column.id === "select"),
    [columns]
  );

  useEffect(() => {
    onTableInit?.(table);
  }, [onTableInit, table]);

  // TODO: Mejorar el skeleton para que se vea mejor y pedir a UX
  if (isLoading)
    return (
      <DataTableSkeleton
        rows={paginationState.pageSize}
        cols={columns.length}
      />
    );

  return (
    <div className="w-full bg-white relative">
      {enableSearch && (
        <div className="flex justify-end p-4 border-b">
          <SearchBar
            value={globalFilter}
            onChange={handleGlobalFilterChange}
            placeholder={searchPlaceholder}
          />
        </div>
      )}
      <div className="rounded-lg border">
        <Table className={className}>
          <TableHeader className={cn("border-none", styles.headerBackground)}>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {enableRowSelection && !hasSelectColumn && (
                  <TableHead className={cn("w-4", styles.headerText)}>
                    <div className="flex items-center">
                      <Checkbox
                        checked={
                          table.getIsAllRowsSelected()
                            ? true
                            : table.getIsSomeRowsSelected()
                            ? "indeterminate"
                            : false
                        }
                        onCheckedChange={(checked) =>
                          table.toggleAllRowsSelected(!!checked)
                        }
                      />
                    </div>
                  </TableHead>
                )}

                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className={cn(styles.headerText)}
                    >
                      <div className="flex items-center gap-2">
                        {header.column.columnDef.enableSorting ? (
                          <>
                            <button
                              type="button"
                              className="flex items-center gap-2"
                              onClick={() =>
                                header.column.toggleSorting(
                                  header.column.getIsSorted() === "asc"
                                )
                              }
                            >
                              {flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                              {header.column.getIsSorted() === "asc" ? (
                                <MdArrowUpward className="size-4" />
                              ) : (
                                <MdArrowDownward className="size-4" />
                              )}
                            </button>
                          </>
                        ) : (
                          <span>
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                          </span>
                        )}
                      </div>
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length > 0 && !isLoading ? (
              customRowsRender ? (
                customRowsRender(table.getRowModel().rows, hasSelectColumn)
              ) : (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.original.id}
                    className={cn(
                      styles.rowHover,
                      row.getIsSelected() ? styles.selectedRow : "",
                      rowClickHandler && "cursor-pointer",
                      "text-dark-gray-1000 font-medium"
                    )}
                    onClick={() => rowClickHandler?.(row)}
                  >
                    {enableRowSelection && !hasSelectColumn && (
                      <TableCell>
                        <div className="flex items-center">
                          <Checkbox
                            checked={row.getIsSelected()}
                            onCheckedChange={row.getToggleSelectedHandler()}
                          />
                        </div>
                      </TableCell>
                    )}

                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={`${row.original.id}-${cell.id}`}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              )
            ) : (
              <TableRow>
                <TableCell
                  className="h-24 text-center"
                  colSpan={columns.length}
                >
                  {emptyMessage || "No hay resultados."}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {table.getRowModel().rows?.length > 0 &&
        isPaginationEnabled &&
        (renderCustomFooter?.(table, paginationState) || (
          <TableFooter pagination={paginationState} table={table} />
        ))}
    </div>
  );
}
