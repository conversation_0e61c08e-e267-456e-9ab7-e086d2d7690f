export type OrderByField = "id" | "plate" | "dvrDeviceId" | "lineNumber";

export interface QueryParams {
  orderBy?: OrderByField;
  searchText?: string;
  pageNumber?: number;
  pageSize?: number;
  vehicleId?: number;
  requestSource?: "UrbetrackAction" | "OnDemand"; // UrbetrackAction is for event videos, OnDemand is for clip videos
  startTime?: string;
  endTime?: string;
}

export const getQueryParams = (params?: QueryParams) => {
  const queryParams = new URLSearchParams();

  if (params?.orderBy) {
    queryParams.append("orderBy", params.orderBy);
  }

  if (params?.searchText) {
    queryParams.append("searchText", params.searchText);
  }

  if (params?.pageNumber) {
    queryParams.append("pageNumber", params.pageNumber.toString());
  }

  if (params?.pageSize) {
    queryParams.append("pageSize", params.pageSize.toString());
  }

  if (params?.vehicleId) {
    queryParams.append("vehicleId", params.vehicleId.toString());
  }

  if (params?.requestSource) {
    queryParams.append("requestSource", params.requestSource);
  }

  if (params?.startTime) {
    queryParams.append("startTime", params.startTime.toString());
  }

  if (params?.endTime) {
    queryParams.append("endTime", params.endTime.toString());
  }

  return queryParams.toString();
};