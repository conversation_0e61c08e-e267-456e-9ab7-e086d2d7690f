import { DropdownType } from "@components/dropdown";
import { useMoveSelectedToTop } from "./use-move-selected-to-top";
import { useSearchableOptions } from "./use-searchable-options";

interface UseFilterableOptionsProps {
  options: DropdownType[];
  selectedValue?: string | string[] | null;
  moveSelectedToTop?: boolean;
}

export const useFilterableOptions = ({
  options,
  selectedValue,
  moveSelectedToTop = false,
}: UseFilterableOptionsProps) => {
  const { searchQuery, setSearchQuery, filteredBySearch, searchProps } =
    useSearchableOptions({ options });

  const finalOptions = useMoveSelectedToTop({
    options: filteredBySearch,
    selectedValue,
    enabled: moveSelectedToTop,
  });

  return {
    searchQuery,
    setSearchQuery,
    filteredOptions: finalOptions,
    searchProps,
  };
};
