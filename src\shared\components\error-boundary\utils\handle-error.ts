export const handleError = (error: Error, context: string) => {
  console.error(`[${context}]:`, error);

  // You can extend this to send errors to a logging service
  // Example: Sentry, LogRocket, or custom error reporting
  if (process.env.NODE_ENV === "production") {
    // Send to error reporting service
    // errorReportingService.captureException(error, { context });
  }

  // You can also show user-friendly notifications here
  // toast.error("Algo salió mal. Por favor, intenta nuevamente.");
};