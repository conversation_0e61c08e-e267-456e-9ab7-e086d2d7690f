
import { DropdownType } from "@components/dropdown";
import { VehicleStatus } from "@/shared/utils/vehicle-status";
import { VehiclesResponse } from "../api/get-vehicles";

export const vehiclesMapper = (input: VehiclesResponse[]): DropdownType[] => {
  return input.map((o) => ({
    id: o.id,
    key: o.metadata.plate,
    text: `${o.metadata.plate} - ${o.metadata.unit}`,
    disabled: false,
    extra: {
      status: o.connection as VehicleStatus,
      cameras: o.metadata.cameras,
      dvrDeviceId: o.metadata.dvrDeviceId,
    },
  }));
};
