import * as React from "react";
import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { Check, Minus } from "lucide-react";

import { cn } from "@styles/cn";

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> & {
    partialChecked?: boolean;
    checked?: boolean | "indeterminate";
    onCheckedChange?: (checked: boolean) => void;
  }
>(({ className, partialChecked, onCheckedChange, ...props }, ref) => {
  const handleCheckedChange = React.useCallback(
    (checked: boolean) => {
      if (onCheckedChange) {
        onCheckedChange(checked);
      }
    },
    [onCheckedChange]
  );

  return (
    <CheckboxPrimitive.Root
      ref={ref}
      className={cn(
        "peer h-4 w-4 shrink-0 rounded-sm border border-slate-800 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-slate-800 data-[state=checked]:text-white",
        partialChecked && "bg-slate-800 text-white",
        className
      )}
      onCheckedChange={handleCheckedChange}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        className={cn("flex items-center justify-center text-current mt-[1px]")}
      >
        <Check className="size-3" strokeWidth={3} />
      </CheckboxPrimitive.Indicator>

      {!partialChecked && (
        <div className="flex items-center justify-center text-current opacity-0 transition-opacity hover:opacity-100">
          <Check className="size-3" strokeWidth={3} />
        </div>
      )}

      {partialChecked && !props.checked && (
        <div className="flex items-center justify-center bg-slate-800">
          <Minus className="size-3" strokeWidth={3} />
        </div>
      )}
    </CheckboxPrimitive.Root>
  );
});

Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export { Checkbox };
