"use no memo";
import {
  ColumnDef,
  flexRender,
  getExpandedRowModel,
  getGroupedRowModel,
  PaginationState,
  Table as ReactTable,
  Row,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";

import { DataTable, TableData } from "./data-table";
import { TableCell, TableRow } from "./table";
import { Checkbox } from "../checkbox/checkbox";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../accordion/accordion";

import Toolbar from "./toolbar";
import TableDetails from "./table-details";
import { cn } from "@styles/cn";

interface DataTableProps<TData> {
  columns: ColumnDef<TableData<TData>>[];
  data: TableData<TData>[];
  pageSize?: number;
  emptyMessage?: string;
  enablePagination?: boolean;
  dataFilter?: (data: TData) => boolean;
  renderCustomFooter?: (
    table: ReactTable<TableData<TData>>,
    pagination: PaginationState
  ) => React.JSX.Element;
  styles?: Record<keyof typeof defaultStyles, string>;
  enableRowSelection?: boolean;
  onRenderDetails?: (row: Row<TableData<TData>>) => React.JSX.Element;
  searchByColumn?: string[];
}

const defaultStyles = {
  headerBackground: "bg-[#F5F1F9]",
  headerText: "text-[#6E4299]",
  rowHover: "hover:bg-[#F8F6FA]",
  selectedRow: "bg-[#F8F6FA]",
};

export function DynamicTable<TData>({
  columns,
  data,
  pageSize = 10,
  emptyMessage,
  enablePagination = true,
  renderCustomFooter,
  styles = defaultStyles,
  enableRowSelection = true,
  onRenderDetails,
  searchByColumn,
}: DataTableProps<TData>) {
  const [accordionGroup, setAccordionGroup] = useState({});
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRowDetails, setSelectedRowDetails] =
    useState<Row<TableData<TData>>>();
  const [groupField, setGroupField] = useState<string>("");
  const [visibleColumns, setVisibleColumns] = useState(
    columns.map((col) => col.id as string)
  );

  const onRowClicked = (row: Row<TableData<TData>>) => {
    setSelectedRowDetails(
      selectedRowDetails?.id === row.original.id ? undefined : row
    );
  };

  const filtereredData = useMemo(() => {
    if (!searchTerm || !searchByColumn) return data;

    return data.filter((d) => {
      return searchByColumn.some((column) => {
        const value = d[column as keyof typeof d] as string;

        return value.toLowerCase().includes(searchTerm);
      });
    });
  }, [data, searchByColumn, searchTerm]);

  const visibleColumnsState = useMemo(() => {
    return columns.reduce((acc, col) => {
      if (!visibleColumns.includes(col.id as string)) {
        acc[col.id as string] = false;
      }

      return acc;
    }, {} as Record<string, boolean>);
  }, [columns, visibleColumns]);

  return (
    <AnimatePresence mode="wait">
      <Toolbar
        columns={columns.map((col) => ({
          id: col.id || "",
          label: col.header as string,
        }))}
        groupField={groupField}
        setGroupField={setGroupField}
        setSearchTerm={setSearchTerm}
        setVisibleColumns={setVisibleColumns}
        visibleColumns={visibleColumns}
      />

      <div className="mt-4 flex">
        <motion.div className="flex-1">
          <DataTable
            className={cn(groupField && "table-fixed")}
            columns={columns}
            customRowsRender={
              groupField
                ? (rows, hasSelectColumn) => (
                    <>
                      {rows.map((row) => {
                        const groupId =
                          row.groupingValue as keyof typeof accordionGroup;
                        const visibleColumnsCount =
                          columns.length +
                          (enableRowSelection && !hasSelectColumn ? 1 : 0) -
                          (groupField ? 1 : 0);

                        return (
                          <Accordion
                            key={row.original.id}
                            asChild
                            className="w-full py-1"
                            type="multiple"
                            value={accordionGroup[groupId]}
                            onValueChange={(value) =>
                              setAccordionGroup((prev) => ({
                                ...prev,
                                [groupId]: value,
                              }))
                            }
                          >
                            <AccordionItem asChild value={groupId}>
                              <>
                                <TableRow key={groupId}>
                                  <td colSpan={visibleColumnsCount}>
                                    <AccordionTrigger className="mr-2 flex w-full px-4 py-3 text-sm">
                                      <div className="select-none">
                                        <span>
                                          {row.groupingValue as string} (
                                          {row.subRows.length})
                                        </span>
                                      </div>
                                    </AccordionTrigger>
                                  </td>
                                </TableRow>

                                <TableRow className="border-none">
                                  <td colSpan={visibleColumnsCount}>
                                    <AccordionContent className="p-0">
                                      {row.subRows.map((subRow) => (
                                        <TableRow
                                          key={subRow.original.id}
                                          className={cn(
                                            "flex items-center pl-4",
                                            styles.rowHover,
                                            subRow.getIsSelected()
                                              ? styles.selectedRow
                                              : "",
                                            onRenderDetails && "cursor-pointer"
                                          )}
                                          onClick={() => onRowClicked(subRow)}
                                        >
                                          {enableRowSelection &&
                                            !hasSelectColumn && (
                                              <div>
                                                <div className="flex items-center">
                                                  <Checkbox
                                                    checked={subRow.getIsSelected()}
                                                    className="data-[state=checked]:bg-[#6E4299]"
                                                    onCheckedChange={subRow.getToggleSelectedHandler()}
                                                  />
                                                </div>
                                              </div>
                                            )}

                                          {subRow
                                            .getVisibleCells()
                                            .map((cell) => (
                                              <TableCell
                                                key={`${subRow.original.id}-${cell.id}`}
                                                className="flex-1 p-4"
                                              >
                                                {flexRender(
                                                  cell.column.columnDef.cell,
                                                  cell.getContext()
                                                )}
                                              </TableCell>
                                            ))}
                                        </TableRow>
                                      ))}
                                    </AccordionContent>
                                  </td>
                                </TableRow>
                              </>
                            </AccordionItem>
                          </Accordion>
                        );
                      })}
                    </>
                  )
                : undefined
            }
            data={filtereredData}
            emptyMessage={emptyMessage}
            enablePagination={enablePagination}
            enableRowSelection={enableRowSelection}
            pageSize={pageSize}
            renderCustomFooter={renderCustomFooter}
            rowClickHandler={
              onRenderDetails ? (row) => onRowClicked(row) : undefined
            }
            styles={{
              ...styles,
              headerText: cn(styles.headerText, "first:!pr-2"),
            }}
            tableConfig={
              groupField
                ? {
                    groupedColumnMode: "reorder",
                    getGroupedRowModel: getGroupedRowModel(),
                    getExpandedRowModel: getExpandedRowModel(),
                    state: {
                      columnVisibility: {
                        [groupField]: false,
                        ...visibleColumnsState,
                      },
                    },
                  }
                : {
                    state: {
                      columnVisibility: visibleColumnsState,
                    },
                  }
            }
            onTableInit={(table) => {
              table.setGrouping([groupField]);
            }}
          />
        </motion.div>
        <TableDetails
          selectedRowDetails={selectedRowDetails}
          setSelectedRowDetails={setSelectedRowDetails}
          onRenderDetails={onRenderDetails}
        />
      </div>
    </AnimatePresence>
  );
}
