import { cn } from "@styles/cn";
import { Button } from "../button/button";

export default function ButtonPopup({
  icon,
  text,
  onClick,
  className,
}: {
  icon?: React.ReactNode;
  className?: string;
  text: string;
  onClick?: () => void;
}) {
  return (
    <Button
      className={cn(
        "h-auto w-full justify-start gap-1 rounded-full py-1 pl-5 pr-2 hover:bg-[#FAF5FF]",
        className
      )}
      variant="link"
      onClick={onClick}
    >
      {icon}
      <span className="text-xs">{text}</span>
    </Button>
  );
}
