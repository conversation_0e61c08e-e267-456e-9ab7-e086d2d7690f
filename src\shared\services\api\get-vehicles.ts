import { get } from "./common";
import { billingServiceAxiosInstance } from "@services/axios";

export type VehiclesResponse = {
  id: string;
  planId: number;
  exVehicleId: number;
  exCompanyId: number;
  type: string;
  state: string;
  connection: string;
  metadata: {
    sim: string;
    line: string;
    plate: string;
    company: string;
    unit: string;
    dvrModel: string;
    dvrDeviceId: string;
    cameras: {
      id: string;
      name: string;
    }[];
  };
};

// GET /api/company/{companyId}/vehicles?planId=1
export const getVehicles =
  <TMappedOutput>(
    mapper: (response: VehiclesResponse[]) => TMappedOutput,
    token: string
  ) =>
  async (companyId: number, planId?: number): Promise<TMappedOutput> => {
    const queryParams = planId ? `planId=${planId}` : "";

    return mapper(
      await get<VehiclesResponse[]>(
        {
          axios: billingServiceAxiosInstance,
          url: `/api/company/${companyId}/vehicles?${queryParams}`,
          token,
          withCredentials: false,
        }
      )
    );
  };
