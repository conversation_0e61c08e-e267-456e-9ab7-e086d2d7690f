export function getFilterTitle(filterComponentName: string, dynamicLabel?: string) {
  if (filterComponentName === "DynamicFilter" && dynamicLabel) {
    return dynamicLabel;
  }
  
  switch (filterComponentName) {
    case "CompanyFilter":
      return "Distrito";
    case "VehicleFilter":
      return "Veh<PERSON><PERSON>lo";
    case "VehicleTypeFilter":
      return "Tipo de vehículo";
    case "DateFilter":
      return "Fecha o período";
    case "EventFilter":
      return "Evento";
    case "VideoStatusFilter":
      return "Estado del video";
    case "DriverFilter":
      return "Chofer";
    case "OwnerFilter":
      return "Responsable";
    case "DynamicFilter":
      return "Filtro Dinámico";
    default:
      return "Filtro";
  }
}