import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { 
  AdvancedDateFilterState, 
  FilterMode,
  DateRange,
  FreeSelection,
  PersistentConfig 
} from '../types/advanced-date-filter.types';
import { validateAdvancedDateFilter } from '../utils/validation-rules';
import { createCompletePeriod } from '../utils/date-helpers';

interface AdvancedDateFilterStore extends AdvancedDateFilterState {
  // Actions for mode management
  setMode: (mode: FilterMode) => void;
  
  // Actions for complete periods
  addCompletePeriod: (year: number, month: number) => void;
  removeCompletePeriod: (periodId: string) => void;
  clearCompletePeriods: () => void;
  
  // Actions for free selection
  addSpecificDate: (date: Date) => void;
  removeSpecificDate: (date: Date) => void;
  addDateRange: (startDate: Date, endDate: Date) => void;
  removeDateRange: (rangeId: string) => void;
  setFreeSelectionMode: (mode: 'specific-days' | 'date-ranges') => void;
  clearFreeSelection: () => void;
  
  // Validation and state management
  validateState: () => void;
  resetToDefaults: () => void;
  
  // Integration with existing filter system
  getOptimizedDateRange: () => { startDate: Date; endDate: Date } | null;
  getFormattedDateRanges: () => { fromDate: string; toDate: string }[];
  
  // Configuration
  config: PersistentConfig;
  updateConfig: (config: Partial<PersistentConfig>) => void;
}

const defaultConfig: PersistentConfig = {
  defaultMode: 'complete-periods',
  favoritePeriodsPresets: [],
  recentSelections: [],
  showValidationHints: true,
  compactView: false,
  maxSelectablePeriods: 12,
  maxSelectableDates: 31
};

const initialState: AdvancedDateFilterState = {
  mode: 'complete-periods',
  completePeriods: [],
  freeSelection: {
    selectedDates: [],
    dateRanges: [],
    mode: 'specific-days'
  },
  isValid: false,
  validationErrors: []
};

export const useAdvancedDateFilterStore = create<AdvancedDateFilterStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      config: defaultConfig,

      setMode: (mode) => {
        set((state) => {
          const newState = { ...state, mode };
          const validation = validateAdvancedDateFilter(newState, {
            maxPeriods: state.config.maxSelectablePeriods,
            maxDates: state.config.maxSelectableDates
          });
          
          return {
            ...newState,
            isValid: validation.isValid,
            validationErrors: validation.errors
          };
        }, false, 'setMode');
      },

      addCompletePeriod: (year, month) => {
        set((state) => {
          const newPeriod = createCompletePeriod(year, month);
          
          // Check if period already exists
          const exists = state.completePeriods.some(p => 
            p.year === year && p.month === month
          );
          
          if (exists) return state;
          
          const newPeriods = [...state.completePeriods, newPeriod];
          const newState = { ...state, completePeriods: newPeriods };
          
          const validation = validateAdvancedDateFilter(newState, {
            maxPeriods: state.config.maxSelectablePeriods,
            maxDates: state.config.maxSelectableDates
          });
          
          return {
            ...newState,
            isValid: validation.isValid,
            validationErrors: validation.errors
          };
        }, false, 'addCompletePeriod');
      },

      removeCompletePeriod: (periodId) => {
        set((state) => {
          const newPeriods = state.completePeriods.filter(p => p.id !== periodId);
          const newState = { ...state, completePeriods: newPeriods };
          
          const validation = validateAdvancedDateFilter(newState, {
            maxPeriods: state.config.maxSelectablePeriods,
            maxDates: state.config.maxSelectableDates
          });
          
          return {
            ...newState,
            isValid: validation.isValid,
            validationErrors: validation.errors
          };
        }, false, 'removeCompletePeriod');
      },

      clearCompletePeriods: () => {
        set((state) => {
          const newState = { ...state, completePeriods: [] };
          const validation = validateAdvancedDateFilter(newState, {
            maxPeriods: state.config.maxSelectablePeriods,
            maxDates: state.config.maxSelectableDates
          });
          
          return {
            ...newState,
            isValid: validation.isValid,
            validationErrors: validation.errors
          };
        }, false, 'clearCompletePeriods');
      },

      addSpecificDate: (date) => {
        set((state) => {
          // Check if date already exists
          const exists = state.freeSelection.selectedDates.some(d => 
            d.getTime() === date.getTime()
          );
          
          if (exists) return state;
          
          const newDates = [...state.freeSelection.selectedDates, date];
          const newFreeSelection = { ...state.freeSelection, selectedDates: newDates };
          const newState = { ...state, freeSelection: newFreeSelection };
          
          const validation = validateAdvancedDateFilter(newState, {
            maxPeriods: state.config.maxSelectablePeriods,
            maxDates: state.config.maxSelectableDates
          });
          
          return {
            ...newState,
            isValid: validation.isValid,
            validationErrors: validation.errors
          };
        }, false, 'addSpecificDate');
      },

      removeSpecificDate: (date) => {
        set((state) => {
          const newDates = state.freeSelection.selectedDates.filter(d => 
            d.getTime() !== date.getTime()
          );
          const newFreeSelection = { ...state.freeSelection, selectedDates: newDates };
          const newState = { ...state, freeSelection: newFreeSelection };
          
          const validation = validateAdvancedDateFilter(newState, {
            maxPeriods: state.config.maxSelectablePeriods,
            maxDates: state.config.maxSelectableDates
          });
          
          return {
            ...newState,
            isValid: validation.isValid,
            validationErrors: validation.errors
          };
        }, false, 'removeSpecificDate');
      },

      addDateRange: (startDate, endDate) => {
        set((state) => {
          const newRange: DateRange = {
            id: Math.random().toString(36).substr(2, 9),
            startDate,
            endDate
          };
          
          const newRanges = [...state.freeSelection.dateRanges, newRange];
          const newFreeSelection = { ...state.freeSelection, dateRanges: newRanges };
          const newState = { ...state, freeSelection: newFreeSelection };
          
          const validation = validateAdvancedDateFilter(newState, {
            maxPeriods: state.config.maxSelectablePeriods,
            maxDates: state.config.maxSelectableDates
          });
          
          return {
            ...newState,
            isValid: validation.isValid,
            validationErrors: validation.errors
          };
        }, false, 'addDateRange');
      },

      removeDateRange: (rangeId) => {
        set((state) => {
          const newRanges = state.freeSelection.dateRanges.filter(r => r.id !== rangeId);
          const newFreeSelection = { ...state.freeSelection, dateRanges: newRanges };
          const newState = { ...state, freeSelection: newFreeSelection };
          
          const validation = validateAdvancedDateFilter(newState, {
            maxPeriods: state.config.maxSelectablePeriods,
            maxDates: state.config.maxSelectableDates
          });
          
          return {
            ...newState,
            isValid: validation.isValid,
            validationErrors: validation.errors
          };
        }, false, 'removeDateRange');
      },

      setFreeSelectionMode: (mode) => {
        set((state) => ({
          ...state,
          freeSelection: { ...state.freeSelection, mode }
        }), false, 'setFreeSelectionMode');
      },

      clearFreeSelection: () => {
        set((state) => {
          const newFreeSelection: FreeSelection = {
            selectedDates: [],
            dateRanges: [],
            mode: state.freeSelection.mode
          };
          const newState = { ...state, freeSelection: newFreeSelection };
          
          const validation = validateAdvancedDateFilter(newState, {
            maxPeriods: state.config.maxSelectablePeriods,
            maxDates: state.config.maxSelectableDates
          });
          
          return {
            ...newState,
            isValid: validation.isValid,
            validationErrors: validation.errors
          };
        }, false, 'clearFreeSelection');
      },

      validateState: () => {
        set((state) => {
          const validation = validateAdvancedDateFilter(state, {
            maxPeriods: state.config.maxSelectablePeriods,
            maxDates: state.config.maxSelectableDates
          });
          
          return {
            ...state,
            isValid: validation.isValid,
            validationErrors: validation.errors
          };
        }, false, 'validateState');
      },

      resetToDefaults: () => {
        set((state) => ({
          ...initialState,
          config: state.config
        }), false, 'resetToDefaults');
      },

      getOptimizedDateRange: () => {
        const state = get();
        
        if (state.mode === 'complete-periods' && state.completePeriods.length > 0) {
          const sortedPeriods = [...state.completePeriods].sort((a, b) => {
            if (a.year !== b.year) return a.year - b.year;
            return a.month - b.month;
          });
          
          return {
            startDate: sortedPeriods[0].startDate,
            endDate: sortedPeriods[sortedPeriods.length - 1].endDate
          };
        }
        
        if (state.mode === 'free-selection') {
          const allDates: Date[] = [...state.freeSelection.selectedDates];
          
          state.freeSelection.dateRanges.forEach(range => {
            allDates.push(range.startDate, range.endDate);
          });
          
          if (allDates.length > 0) {
            const sortedDates = allDates.sort((a, b) => a.getTime() - b.getTime());
            return {
              startDate: sortedDates[0],
              endDate: sortedDates[sortedDates.length - 1]
            };
          }
        }
        
        return null;
      },

      getFormattedDateRanges: () => {
        const state = get();
        const ranges: { fromDate: string; toDate: string }[] = [];
        
        if (state.mode === 'complete-periods') {
          state.completePeriods.forEach(period => {
            ranges.push({
              fromDate: period.startDate.toISOString().split('T')[0],
              toDate: period.endDate.toISOString().split('T')[0]
            });
          });
        } else {
          // For free selection, create ranges for each date/range
          state.freeSelection.selectedDates.forEach(date => {
            const dateStr = date.toISOString().split('T')[0];
            ranges.push({
              fromDate: dateStr,
              toDate: dateStr
            });
          });
          
          state.freeSelection.dateRanges.forEach(range => {
            ranges.push({
              fromDate: range.startDate.toISOString().split('T')[0],
              toDate: range.endDate.toISOString().split('T')[0]
            });
          });
        }
        
        return ranges;
      },

      updateConfig: (newConfig) => {
        set((state) => ({
          ...state,
          config: { ...state.config, ...newConfig }
        }), false, 'updateConfig');
      }
    }),
    {
      name: "advanced-date-filter-store",
    }
  )
);

// Selector hooks for easier component integration
export const useAdvancedDateFilterMode = () => 
  useAdvancedDateFilterStore((state) => state.mode);

export const useCompletePeriods = () => 
  useAdvancedDateFilterStore((state) => state.completePeriods);

export const useFreeSelection = () => 
  useAdvancedDateFilterStore((state) => state.freeSelection);

export const useAdvancedDateFilterValidation = () => 
  useAdvancedDateFilterStore((state) => ({
    isValid: state.isValid,
    errors: state.validationErrors
  }));

export const useAdvancedDateFilterConfig = () => 
  useAdvancedDateFilterStore((state) => state.config);