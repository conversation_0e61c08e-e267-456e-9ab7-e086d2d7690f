import { cn } from "@styles/cn";
import { Button } from "@components/button";
import { XIcon } from "lucide-react";
import { forwardRef, type ReactNode } from "react";

type MiniDrawerProps = {
  title: string | null;
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
};

export const MiniDrawer = forwardRef<HTMLDivElement, MiniDrawerProps>(
  ({ title, isOpen, onClose, children }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "fixed w-80 h-[70vh] rounded-sm transition-right duration-300 ease-in-out bg-zinc-50 shadow-lg z-[-1] flex flex-col",
          isOpen ? "right-[22rem]" : "right-[-22rem]",
          "top-1/6"
        )}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-4 w-full flex-shrink-0">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">{title}</h3>
            <div className="flex items-center gap-2">
              <Button variant="link" className="justify-end" onClick={onClose}>
                <XIcon />
              </Button>
            </div>
          </div>
        </div>
        <div className="flex-1 overflow-y-auto px-4 pb-4 h-0">{children}</div>
      </div>
    );
  }
);

MiniDrawer.displayName = "MiniDrawer";
