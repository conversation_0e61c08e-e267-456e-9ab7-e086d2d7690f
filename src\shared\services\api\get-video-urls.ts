import { get } from "./common";
import { functionsAxiosInstance } from "../axios";
import { MediaRequest } from "./post-media-request";

export const getVideoUrls =
  <T>(mapper: (response: MediaRequest[]) => T) =>
  async (companyId: number, eventId: number, telemetry: 0 | 1 = 0): Promise<T> => {
    return mapper(
      await get<MediaRequest[]>( 
        {
          axios: functionsAxiosInstance,
          url: `GetMediaDownloadRequestFunctionV2?companyId=${companyId}&eventId=${eventId}&telemetry=${telemetry}`,
        }
      )
    );
  };
