import { ButtonWithIcon } from "@/shared/components/ui/button/button-with-icon";
import { UrbeDrawer } from "@/shared/components/ui/drawer";
import { MdOutlineInfo } from "react-icons/md";
import { useState } from "react";
import { useSearchParams } from "react-router";

export function IntegrationInformationDrawer() {
  const [searchParams] = useSearchParams();
  const tab = searchParams.get("tab");
  const isReportTab = tab === "reporte-f14" || tab === "reporte-f34" || !tab;
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const openDrawer = () => setIsDrawerOpen(true);
  const closeDrawer = () => setIsDrawerOpen(false);

  const getReportInfo = () => {
    const currentTab = tab || "reporte-f14";
    if (currentTab === "reporte-f14") {
      return {
        title: "Información del Reporte F14",
        description: "El reporte F14 contiene información sobre los residuos sólidos urbanos recolectados y transportados a sitios de disposición final.",
        fields: [
          "NUAP (Número Único de Autorización de Pesaje)",
          "Información del vehículo y conductor",
          "Datos de llegada y microruta",
          "Toneladas por tipo de residuo",
          "Información de compensación"
        ]
      };
    } else {
      return {
        title: "Información del Reporte F34",
        description: "El reporte F34 registra la información de entrada y salida de vehículos en sitios de disposición final.",
        fields: [
          "NUSD (Número Único del Sitio de Disposición)",
          "Información de la empresa transportadora",
          "Datos de llegada y salida",
          "Peso y tonelaje del servicio",
          "Relación con reporte F14"
        ]
      };
    }
  };

  const reportInfo = getReportInfo();

  return (
    <UrbeDrawer
      title="Información de Integración"
      open={isDrawerOpen}
      onOpenChange={(open) => {
        if (!open) {
          closeDrawer();
        }
      }}
      trigger={
        isReportTab ? (
          <ButtonWithIcon
            variant="link"
            icon={<MdOutlineInfo />}
            text="Información de Integración"
            onClick={openDrawer}
          />
        ) : null
      }
    >
      <div className="p-6">
        <h2 className="text-xl font-bold mb-4">{reportInfo.title}</h2>
        <p className="text-gray-700 mb-4">
          {reportInfo.description}
        </p>
        <div className="mb-4">
          <h3 className="text-lg font-semibold mb-2">Campos principales:</h3>
          <ul className="list-disc list-inside space-y-1 text-gray-600">
            {reportInfo.fields.map((field, index) => (
              <li key={index}>{field}</li>
            ))}
          </ul>
        </div>
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-2 text-blue-800">Integración Mercury</h3>
          <p className="text-blue-700 text-sm">
            Los datos se obtienen directamente de la API de Mercury utilizando filtros de fecha.
            Seleccione un rango de fechas en el panel de filtros para visualizar los reportes.
          </p>
        </div>
      </div>
    </UrbeDrawer>
  );
}