import clsx from 'clsx';

const generalStyles = [
  'block w-full rounded',
  'py-form-control-input-v pl-form-control-input-h pr-12',
  'text-form font-form text-system-dark-text truncate',
  'select-custom-icon',
];
const border = [
  'border border-primary-inactive',
  'focus:py-form-control-input-v focus:pl-form-control-input-h',
  'hover:py-form-control-input-v-hover hover:px-form-control-input-h-hover',
];
const error = [
  'select-error-icon border-2 border-system-red-main pr-20',
  'focus:py-form-control-input-v-focus focus:pl-form-control-input-h-focus',
];
const readOnly = 'bg-system-light-readonly';

const enabled = [
  'enabled:hover:border-2 enabled:hover:border-primary-hover', // Hover
  'enabled:focus:ring-4 enabled:focus:ring-primary-focus enabled:focus:border enabled:focus:border-primary enabled:focus:shadow-level-inset', // Focus
  'enabled:active:border enabled:active:border-system-dark-text', // Active
];

const disabled = [
  'disabled:hover:py-form-control-input-v disabled:hover:pl-form-control-input-h',
  'disabled:text-system-light-disabled',
  'disabled:placeholder:text-system-light-disabled',
  'disabled:cursor-not-allowed',
  'disabled:shadow-none',
];

export const getSelectStyles = (hasError: boolean, isReadonly: boolean) => {
  return clsx(hasError && error, !hasError && border, isReadonly && readOnly, generalStyles, enabled, disabled);
};
