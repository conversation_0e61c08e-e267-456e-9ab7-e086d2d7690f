import { ColumnDef } from "@tanstack/react-table";
import { F14ReportTableData } from "@/shared/services/mappers/reports-mapper";

export const columnsF14Reports: ColumnDef<F14ReportTableData>[] = [
  {
    accessorKey: "nuap",
    header: "NUAP",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("nuap")}</div>
    ),
  },
  {
    accessorKey: "licensePlate",
    header: "Placa",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("licensePlate")}</div>
    ),
  },
  {
    accessorKey: "destinationType",
    header: "Tipo Destino",
    cell: ({ row }) => (
      <div>{row.getValue("destinationType")}</div>
    ),
  },
  {
    accessorKey: "destinationCode",
    header: "Código <PERSON>tino",
    cell: ({ row }) => (
      <div>{row.getValue("destinationCode")}</div>
    ),
  },
  {
    accessorKey: "vehicleArrival",
    header: "<PERSON>cha Llegada",
    cell: ({ row }) => (
      <div>{row.getValue("vehicleArrival")}</div>
    ),
  },
  {
    accessorKey: "vehicleArrivalTime",
    header: "Hora Llegada",
    cell: ({ row }) => (
      <div>{row.getValue("vehicleArrivalTime")}</div>
    ),
  },
  {
    accessorKey: "microrouteId",
    header: "ID Microruta",
    cell: ({ row }) => (
      <div>{row.getValue("microrouteId")}</div>
    ),
  },
  {
    accessorKey: "totalTons",
    header: "Total Toneladas",
    cell: ({ row }) => {
      const tons = parseFloat(row.getValue("totalTons"));
      return <div className="font-medium">{tons.toFixed(3)} t</div>;
    },
  },
  {
    accessorKey: "urbanCleaningTons",
    header: "Aseo Urbano (t)",
    cell: ({ row }) => {
      const tons = parseFloat(row.getValue("urbanCleaningTons"));
      return <div>{tons.toFixed(3)}</div>;
    },
  },
  {
    accessorKey: "sweepingTons",
    header: "Barrido (t)",
    cell: ({ row }) => {
      const tons = parseFloat(row.getValue("sweepingTons"));
      return <div>{tons.toFixed(3)}</div>;
    },
  },
  {
    accessorKey: "nonRecyclableTons",
    header: "No Reciclable (t)",
    cell: ({ row }) => {
      const tons = parseFloat(row.getValue("nonRecyclableTons"));
      return <div>{tons.toFixed(3)}</div>;
    },
  },
  {
    accessorKey: "recyclableTons",
    header: "Reciclable (t)",
    cell: ({ row }) => {
      const tons = parseFloat(row.getValue("recyclableTons"));
      return <div>{tons.toFixed(3)}</div>;
    },
  },
  {
    accessorKey: "rejectedTons",
    header: "Rechazado (t)",
    cell: ({ row }) => {
      const tons = parseFloat(row.getValue("rejectedTons"));
      return <div>{tons.toFixed(3)}</div>;
    },
  },
  {
    accessorKey: "recyclingArea",
    header: "Área Reciclaje",
    cell: ({ row }) => (
      <div>{row.getValue("recyclingArea")}</div>
    ),
  },
  {
    accessorKey: "extendedRouteCode",
    header: "Código Ruta Ext.",
    cell: ({ row }) => (
      <div>{row.getValue("extendedRouteCode")}</div>
    ),
  },
  {
    accessorKey: "serviceTicketId",
    header: "ID Ticket",
    cell: ({ row }) => (
      <div>{row.getValue("serviceTicketId")}</div>
    ),
  },
  {
    accessorKey: "toll",
    header: "Peaje",
    cell: ({ row }) => {
      const toll = parseFloat(row.getValue("toll"));
      return <div>${toll.toLocaleString()}</div>;
    },
  },
  {
    accessorKey: "compensationRelation",
    header: "Compensación",
    cell: ({ row }) => {
      const hasCompensation = row.getValue("compensationRelation");
      return (
        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
          hasCompensation 
            ? "bg-green-100 text-green-800" 
            : "bg-gray-100 text-gray-800"
        }`}>
          {hasCompensation ? "Sí" : "No"}
        </div>
      );
    },
  },
];