import { DropdownType } from "@components/dropdown";
import { useQuery } from "@tanstack/react-query";

export const usePlansQuery = ({
  getPlans,
  companyId,
  mapperKey,
}: {
  getPlans: (companyId: number) => Promise<DropdownType[]>;
  companyId: number | null;
  mapperKey: string;
}) => {
  const result = useQuery({
    queryKey: [mapperKey, companyId],
    queryFn: () => getPlans(companyId!),
    enabled: !!companyId,
  });

  const { isPending: isLoading, data, error } = result;

  return { isLoading, options: data ?? [], error };
};
