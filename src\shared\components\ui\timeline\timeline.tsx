import {
  Line<PERSON><PERSON>,
  Line,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON>r,
  ReferenceLine,
  <PERSON>lt<PERSON>,
  CartesianGrid,
} from "recharts";

export interface TimelineData {
  timeInSeconds: number;
  [key: string]: unknown;
}

export interface LineConfig {
  dataKey: string;
  stroke: string;
  strokeWidth?: number;
  dotColor?: string;
  activeDotColor?: string;
}

interface TimelineProps<T extends TimelineData> {
  data: T[];
  currentTime: number;
  onTimelineClick: (timeInSeconds: number) => void;
  lines: LineConfig[];
  yAxisDomain?: [string | number, string | number];
  yAxisWidth?: number;
  yAxisTickFormatter?: (value: number) => string;
  xAxisTickFormatter?: (value: number) => string;
  customTooltip?: React.ComponentType<{
    active?: boolean;
    payload?: Array<{
      payload: T;
      dataKey?: string;
      value?: number;
      color?: string;
    }>;
  }>;
  emptyStateIcon?: string;
  emptyStateMessage?: string;
  height?: string;
}

export function Timeline<T extends TimelineData>({
  data,
  currentTime,
  onTimelineClick,
  lines,
  yAxisDomain = ["dataMin - 5", "dataMax + 5"],
  yAxisWidth = 30,
  yAxisTickFormatter,
  xAxisTickFormatter,
  customTooltip: CustomTooltip,
  emptyStateIcon = "📊",
  emptyStateMessage = "No data available",
  height = "h-52",
}: TimelineProps<T>) {
  const handleChartClick = (chartData: {
    activePayload?: { payload: T }[];
  }) => {
    if (
      chartData &&
      chartData.activePayload &&
      chartData.activePayload.length > 0
    ) {
      const clickedTime = chartData.activePayload[0].payload.timeInSeconds;
      onTimelineClick(clickedTime);
    }
  };

  if (!data || data.length === 0) {
    return (
      <div
        className={`${height} flex items-center justify-center bg-gray-50 rounded`}
      >
        <div className="text-center">
          <div className="text-neutral-400 mb-1">{emptyStateIcon}</div>
          <p className="text-sm text-gray-600">{emptyStateMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`${height} w-full`}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
          onClick={handleChartClick}
          style={{ cursor: "pointer" }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="timeInSeconds"
            type="number"
            scale="linear"
            domain={["dataMin", "dataMax"]}
            tickFormatter={xAxisTickFormatter}
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 10, fill: `hsl(var(--neutral-400))` }}
            tickCount={7}
            interval="preserveStartEnd"
          />
          <YAxis
            domain={yAxisDomain}
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 10, fill: `hsl(var(--neutral-400))` }}
            width={yAxisWidth}
            tickFormatter={yAxisTickFormatter}
          />
          {CustomTooltip && <Tooltip content={<CustomTooltip />} />}
          <ReferenceLine
            x={currentTime}
            stroke={`hsl(var(--secondary-1000))`}
            strokeWidth={2}
            strokeDasharray="0"
          />
          {lines.map((lineConfig) => (
            <Line
              key={lineConfig.dataKey}
              type="monotone"
              dataKey={lineConfig.dataKey}
              stroke={lineConfig.stroke}
              strokeWidth={lineConfig.strokeWidth || 2}
              dot={{
                r: 2,
                fill: lineConfig.dotColor || lineConfig.stroke,
              }}
              activeDot={{
                r: 4,
                fill:
                  lineConfig.activeDotColor ||
                  lineConfig.dotColor ||
                  lineConfig.stroke,
                stroke: `hsl(var(--neutral-white))`,
                strokeWidth: 2,
              }}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
