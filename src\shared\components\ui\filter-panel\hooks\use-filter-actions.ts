import { MouseEvent as ReactMouseEvent, type ReactElement } from "react";

import type { FilterValue } from "@components/filter-panel/stores/dynamic-filter-store";
import { isFilterDisabled, isFilterLoading } from "../utils/filter-helpers";

type UseFilterActionsProps = {
  // Filter store actions
  clearStaticPending: () => void;
  clearDynamicPending: () => void;
  applyStaticFilters: () => void;
  applyPendingFilters: () => void;

  // Filter state
  pendingCompany: string | null;
  appliedCompany: string | null;
  appliedDynamicFilters: Record<string, FilterValue>;

  // UI state
  selectedFilter: ReactElement | null;
  setSelectedFilter: (filter: ReactElement | null) => void;

  // Optional callbacks
  onClearFilters?: () => void;
  onApplyFilters?: () => void;
};

export function useFilterActions({
  clearStaticPending,
  clearDynamicPending,
  applyStaticFilters,
  applyPendingFilters,
  pendingCompany,
  appliedCompany,
  appliedDynamicFilters,
  selectedFilter,
  setSelectedFilter,
  onClearFilters,
  onApplyFilters,
}: UseFilterActionsProps) {
  function handleFilterClick(
    child: ReactElement,
    e: ReactMouseEvent<HTMLLIElement, MouseEvent>
  ) {
    e.stopPropagation();

    const componentName =
      typeof child.type === "function" && "name" in child.type
        ? child.type.name
        : "Unknown";

    // Don't allow clicking on disabled or loading filters
    const isLoading = isFilterLoading(
      componentName,
      child,
      pendingCompany,
      appliedCompany,
      appliedDynamicFilters
    );
    const isDisabled = isFilterDisabled(
      componentName,
      child,
      pendingCompany,
      appliedDynamicFilters
    );

    if (isLoading || isDisabled) {
      return;
    }

    let selectedFilterName;
    if (selectedFilter) {
      selectedFilterName =
        typeof selectedFilter.type === "function" &&
        "name" in selectedFilter.type
          ? selectedFilter.type.name
          : "Unknown";
    }

    setSelectedFilter(selectedFilterName === componentName ? null : child);
  }

  function handleClearFilters() {
    clearStaticPending();
    clearDynamicPending();
    onClearFilters?.();
  }

  function handleApplyFilters() {
    applyStaticFilters();
    applyPendingFilters();
    onApplyFilters?.();
  }

  return {
    handleFilterClick,
    handleClearFilters,
    handleApplyFilters,
  };
}
