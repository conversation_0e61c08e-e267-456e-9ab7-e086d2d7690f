import { useMemo } from "react";
import { DropdownType } from "@components/dropdown";

interface UseMoveSelectedToTopProps {
  options: DropdownType[];
  selectedValue?: string | string[] | null;
  enabled?: boolean;
}

export const useMoveSelectedToTop = ({
  options,
  selectedValue,
  enabled = false,
}: UseMoveSelectedToTopProps) => {
  return useMemo(() => {
    if (!enabled || !selectedValue) {
      return options;
    }

    const selectedOption = options.find(
      (opt) => opt.id.toString() === selectedValue
    );
    const otherOptions = options.filter(
      (opt) => opt.id.toString() !== selectedValue
    );

    if (selectedOption) {
      return [selectedOption, ...otherOptions];
    }

    return options;
  }, [options, selectedValue, enabled]);
};
