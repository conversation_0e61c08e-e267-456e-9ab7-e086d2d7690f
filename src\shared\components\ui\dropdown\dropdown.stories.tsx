import type { StoryFn } from '@storybook/react';

import { DummyForm } from '../../forms/dummyForm';

import { Dropdown } from './dropdown';

export default {
  title: 'Atoms/Dropdown',
  component: Dropdown,
};

const optionsList = [
  { id: 0, text: 'Select an option' },
  { id: 1, text: 'First' },
  { id: 2, text: 'Second' },
  { id: 3, text: 'A very very very very very very very long third option' },
];
const Template: StoryFn<typeof Dropdown> = (args) => {
  return (
    <DummyForm name="dropdown">
      <Dropdown {...args} name="dropdown" />
    </DummyForm>
  );
};

export const DefaultUsage = Template.bind({});
DefaultUsage.args = {
  options: optionsList,
};

export const HasError = Template.bind({});
HasError.args = {
  options: optionsList,
  hasError: true,
};

export const IsDisabled = Template.bind({});
IsDisabled.args = {
  options: optionsList,
  isDisabled: true,
};

export const IsReadOnly = Template.bind({});
IsReadOnly.args = {
  options: optionsList,
  isReadonly: true,
};
