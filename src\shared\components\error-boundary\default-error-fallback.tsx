import { FallbackProps } from "react-error-boundary";

export const DefaultErrorFallback: React.FC<FallbackProps> = ({
  error,
  resetErrorBoundary,
}) => (
  <div className="error-fallback rounded-lg border border-red-200 bg-red-50 p-6">
    <h2 className="mb-2 text-xl font-semibold text-red-800">Algo salió mal</h2>
    <p className="mb-4 text-red-700">
      Ocurrió un error en este componente. Por favor, intenta nuevamente.
    </p>

    {error && (
      <details className="mb-4">
        <summary className="cursor-pointer text-red-600 hover:text-red-800">
          Detalles del error
        </summary>
        <pre className="mt-2 overflow-auto rounded bg-red-100 p-3 text-sm text-red-800">
          {error.message}
        </pre>
      </details>
    )}

    <div className="flex gap-2">
      <button
        className="rounded bg-red-600 px-4 py-2 text-white transition-colors hover:bg-red-700"
        type="button"
        onClick={resetErrorBoundary}
      >
        Intentar nuevamente
      </button>
      <button
        className="bg-light-dark-600 hover:bg-light-dark-700 rounded px-4 py-2 text-white transition-colors"
        type="button"
        onClick={() => window.location.reload()}
      >
        Recargar página
      </button>
    </div>
  </div>
);
