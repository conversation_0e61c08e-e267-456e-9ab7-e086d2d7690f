// F14 Report Types
export interface F14ReportData {
  nuap: number;
  destinationType: string;
  destinationCode: string;
  licensePlate: string;
  vehicleArrival: string;
  vehicleArrivalTime: string;
  microrouteId: number;
  urbanCleaningTons: number;
  sweepingTons: number;
  nonRecyclableTons: number;
  rejectedTons: number;
  recyclableTons: number;
  measuringUnit: number;
  toll: number;
  serviceTicketId: number;
  recyclingArea: string;
  extendedRouteCode: string;
  totalTons: number;
  compensationRelation: boolean;
  compensationRelationTicketId: number | null;
}

// F34 Report Types
export interface F34ReportData {
  nusd: string;
  originType: string;
  placeOriginNumber: number | null;
  companyName: string;
  nit: string;
  daneCode: string;
  licensePlate: string;
  arrivalDate: string;
  arrivalTime: string;
  departureDate: string;
  departureTime: string;
  tons: number;
  serviceTicketId: number;
  serviceTicketWeight: number;
  serviceTicketTonnage: number;
  rejectedTonnage: number;
  existsInReport14: boolean;
  companyNIT: number;
  nuap: number;
}

// Report Type Enum
export type ReportType = 'Format14' | 'Format34';

// API Request Parameters
export interface ReportRequestParams {
  fromDate: string; // YYYY-MM-DD format
  toDate: string;   // YYYY-MM-DD format
  reportType: ReportType;
}

// Generic Report Response
export type ReportResponse<T> = T[];

// Table Data Types (with id for DataTable component)
export interface F14ReportTableData extends F14ReportData {
  id: string;
}

export interface F34ReportTableData extends F34ReportData {
  id: string;
}

// Mappers
export const f14ReportMapper = (data: F14ReportData[]): F14ReportTableData[] => {
  return data.map((item, index) => ({
    ...item,
    id: `f14-${item.serviceTicketId}-${index}`, // Create unique ID
    // Format dates if needed
    vehicleArrival: item.vehicleArrival,
    vehicleArrivalTime: item.vehicleArrivalTime,
    // Ensure numeric values are properly typed
    totalTons: Number(item.totalTons),
    urbanCleaningTons: Number(item.urbanCleaningTons),
    sweepingTons: Number(item.sweepingTons),
    nonRecyclableTons: Number(item.nonRecyclableTons),
    rejectedTons: Number(item.rejectedTons),
    recyclableTons: Number(item.recyclableTons),
  }));
};

export const f34ReportMapper = (data: F34ReportData[]): F34ReportTableData[] => {
  return data.map((item, index) => ({
    ...item,
    id: `f34-${item.serviceTicketId}-${index}`, // Create unique ID
    // Format dates if needed
    arrivalDate: item.arrivalDate,
    arrivalTime: item.arrivalTime,
    departureDate: item.departureDate,
    departureTime: item.departureTime,
    // Ensure numeric values are properly typed
    tons: Number(item.tons),
    serviceTicketWeight: Number(item.serviceTicketWeight),
    serviceTicketTonnage: Number(item.serviceTicketTonnage),
    rejectedTonnage: Number(item.rejectedTonnage),
  }));
};