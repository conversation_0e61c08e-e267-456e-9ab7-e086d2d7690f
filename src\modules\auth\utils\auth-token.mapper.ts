type UserCredentials = {
  id: number;
  email: string;
  name: string;
  employeeId: number;
  companySourceId: number;
};

export const authTokenMapper = (data: {
  userId: UserCredentials["id"];
  mail: UserCredentials["email"];
  name: UserCredentials["name"];
  employeeId: UserCredentials["employeeId"];
}) => {
  return {
    id: data.userId,
    email: data.mail,
    name: data.name,
    employeeId: data.employeeId,
    companySourceId: -1,
  };
};
