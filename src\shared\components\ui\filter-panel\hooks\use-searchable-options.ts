import { DropdownType } from "@components/dropdown";
import { useMemo, useState } from "react";

interface UseSearchableOptionsProps {
  options: DropdownType[];
}

export const useSearchableOptions = ({
  options,
}: UseSearchableOptionsProps) => {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredBySearch = useMemo(() => {
    if (!searchQuery.trim()) {
      return options;
    }

    return options.filter((option) =>
      option.text.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [options, searchQuery]);

  const searchProps = {
    value: searchQuery,
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
    },
  };

  return {
    searchQuery,
    setSearchQuery,
    filteredBySearch,
    searchProps,
  };
};
