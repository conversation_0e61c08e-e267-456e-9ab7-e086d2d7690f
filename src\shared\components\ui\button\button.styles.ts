import { cva } from "class-variance-authority";

export const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm px-4 font-semibold transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 cursor-pointer disabled:cursor-not-allowed",
  {
    variants: {
      variant: {
        primary:
          "bg-primary-800 text-primary-50 hover:bg-primary-600 disabled:bg-primary-300 disabled:text-primary-50",
        secondary:
          "bg-white border-2 border-primary-800 text-primary-800 hover:bg-primary-100 disabled:border-neutral-600 disabled:text-neutral-600",
        link:
          "text-primary-800 hover:bg-primary-100 disabled:text-primary-400",
      },
      size: {
        xs: "h-8 text-xs",
        sm: "h-9 text-xs",
        md: "h-10 text-sm",
        lg: "h-11 text-sm",
        xl: "h-12 text-base",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
    },
  }
);
