import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cn } from "@styles/cn";
import type * as React from "react";

import { TabsContent } from "./components/tabs-content/tabs-content";
import { TabsList } from "./components/tabs-list/tabs-list";
import { TabsTrigger } from "./components/tabs-trigger/tabs-trigger";

type TabItem = {
  value: string;
  label: string;
  content: React.ReactNode;
  icon?: React.ReactNode;
  disabled?: boolean;
};

type TabsProps = {
  items: TabItem[];
  size?: "sm" | "md" | "lg";
} & React.ComponentPropsWithoutRef<typeof TabsPrimitive.Root>;

function Tabs({ items, className, size, ...props }: TabsProps) {
  return (
    <TabsPrimitive.Root className={cn("w-full", className)} {...props}>
      <TabsList size={size} className="pb-1">
        {items.map((tab) => (
          <TabsTrigger
            key={tab.value}
            value={tab.value}
            size={size}
            disabled={tab.disabled}
          >
            {!!tab.icon && <span>{tab.icon}</span>}
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
      {items.map((tab) => (
        <TabsContent key={tab.value} value={tab.value}>
          {tab.content}
        </TabsContent>
      ))}
    </TabsPrimitive.Root>
  );
}

export { Tabs };
