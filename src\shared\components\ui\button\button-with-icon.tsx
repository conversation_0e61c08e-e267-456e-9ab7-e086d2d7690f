import { ReactNode } from "react";
import { Button } from "./button";
import { ButtonProps } from "@components/button";

type ButtonWithIconProps = {
  icon: ReactNode;
  text?: string;
  variant: "primary" | "secondary" | "link";
} & ButtonProps;

export function ButtonWithIcon({
  variant,
  icon,
  text,
  ...props
}: ButtonWithIconProps) {
  return (
    <Button variant={variant} size="md" {...props}>
      {icon} {text}
    </Button>
  );
}
