import { useCallback, useMemo } from 'react';
import { useAdvancedDateFilterStore } from '../stores/advanced-date-filter-store';
import { formatDateForAPI } from '../utils/date-helpers';

export interface UseAdvancedDateFilterReturn {
  // State
  mode: 'complete-periods' | 'free-selection';
  isValid: boolean;
  hasSelections: boolean;
  
  // Date ranges for API calls
  optimizedDateRange: { startDate: Date; endDate: Date } | null;
  formattedDateRanges: { fromDate: string; toDate: string }[];
  
  // Actions
  setMode: (mode: 'complete-periods' | 'free-selection') => void;
  clearAll: () => void;
  resetToDefaults: () => void;
  
  // Validation
  errors: Array<{ type: string; message: string; field?: string }>;
  
  // For legacy compatibility
  legacyDateRange: { fromDate: string | null; toDate: string | null };
}

export const useAdvancedDateFilter = (): UseAdvancedDateFilterReturn => {
  const store = useAdvancedDateFilterStore();

  const hasSelections = useMemo(() => {
    if (store.mode === 'complete-periods') {
      return store.completePeriods.length > 0;
    } else {
      return store.freeSelection.selectedDates.length > 0 || 
             store.freeSelection.dateRanges.length > 0;
    }
  }, [store.mode, store.completePeriods, store.freeSelection]);

  const optimizedDateRange = useMemo(() => {
    return store.getOptimizedDateRange();
  }, [store, store.completePeriods, store.freeSelection]);

  const formattedDateRanges = useMemo(() => {
    return store.getFormattedDateRanges();
  }, [store, store.completePeriods, store.freeSelection]);

  const legacyDateRange = useMemo(() => {
    const range = optimizedDateRange;
    return {
      fromDate: range ? formatDateForAPI(range.startDate) : null,
      toDate: range ? formatDateForAPI(range.endDate) : null
    };
  }, [optimizedDateRange]);

  const clearAll = useCallback(() => {
    if (store.mode === 'complete-periods') {
      store.clearCompletePeriods();
    } else {
      store.clearFreeSelection();
    }
  }, [store]);

  return {
    // State
    mode: store.mode,
    isValid: store.isValid,
    hasSelections,
    
    // Date ranges
    optimizedDateRange,
    formattedDateRanges,
    legacyDateRange,
    
    // Actions
    setMode: store.setMode,
    clearAll,
    resetToDefaults: store.resetToDefaults,
    
    // Validation
    errors: store.validationErrors,
  };
};

// Hook for integration with existing filter system
export const useAdvancedDateFilterIntegration = () => {
  const { legacyDateRange, isValid, hasSelections } = useAdvancedDateFilter();
  
  return {
    fromDate: legacyDateRange.fromDate,
    toDate: legacyDateRange.toDate,
    isValid,
    hasSelections,
    hasDateRange: !!legacyDateRange.fromDate && !!legacyDateRange.toDate && isValid
  };
};