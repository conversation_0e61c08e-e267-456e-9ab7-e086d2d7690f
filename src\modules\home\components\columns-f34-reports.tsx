import { ColumnDef } from "@tanstack/react-table";
import { F34ReportTableData } from "@/shared/services/mappers/reports-mapper";

export const columnsF34Reports: ColumnDef<F34ReportTableData>[] = [
  {
    accessorKey: "nusd",
    header: "NUSD",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("nusd")}</div>
    ),
  },
  {
    accessorKey: "licensePlate",
    header: "Placa",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("licensePlate")}</div>
    ),
  },
  {
    accessorKey: "companyName",
    header: "Empresa",
    cell: ({ row }) => (
      <div className="max-w-[200px] truncate" title={row.getValue("companyName")}>
        {row.getValue("companyName")}
      </div>
    ),
  },
  {
    accessorKey: "nit",
    header: "NIT",
    cell: ({ row }) => (
      <div>{row.getValue("nit")}</div>
    ),
  },
  {
    accessorKey: "daneCode",
    header: "Código DANE",
    cell: ({ row }) => (
      <div>{row.getValue("daneCode")}</div>
    ),
  },
  {
    accessorKey: "originType",
    header: "Tipo Origen",
    cell: ({ row }) => (
      <div>{row.getValue("originType")}</div>
    ),
  },
  {
    accessorKey: "arrivalDate",
    header: "Fecha Llegada",
    cell: ({ row }) => (
      <div>{row.getValue("arrivalDate")}</div>
    ),
  },
  {
    accessorKey: "arrivalTime",
    header: "Hora Llegada",
    cell: ({ row }) => (
      <div>{row.getValue("arrivalTime")}</div>
    ),
  },
  {
    accessorKey: "departureDate",
    header: "Fecha Salida",
    cell: ({ row }) => (
      <div>{row.getValue("departureDate")}</div>
    ),
  },
  {
    accessorKey: "departureTime",
    header: "Hora Salida",
    cell: ({ row }) => (
      <div>{row.getValue("departureTime")}</div>
    ),
  },
  {
    accessorKey: "tons",
    header: "Toneladas",
    cell: ({ row }) => {
      const tons = parseFloat(row.getValue("tons"));
      return <div className="font-medium">{tons.toFixed(3)} t</div>;
    },
  },
  {
    accessorKey: "serviceTicketWeight",
    header: "Peso Ticket (kg)",
    cell: ({ row }) => {
      const weight = parseFloat(row.getValue("serviceTicketWeight"));
      return <div>{weight.toFixed(2)} kg</div>;
    },
  },
  {
    accessorKey: "serviceTicketTonnage",
    header: "Tonelaje Ticket",
    cell: ({ row }) => {
      const tonnage = parseFloat(row.getValue("serviceTicketTonnage"));
      return <div>{tonnage.toFixed(4)} t</div>;
    },
  },
  {
    accessorKey: "rejectedTonnage",
    header: "Tonelaje Rechazado",
    cell: ({ row }) => {
      const rejected = parseFloat(row.getValue("rejectedTonnage"));
      return <div>{rejected.toFixed(3)} t</div>;
    },
  },
  {
    accessorKey: "serviceTicketId",
    header: "ID Ticket",
    cell: ({ row }) => (
      <div>{row.getValue("serviceTicketId")}</div>
    ),
  },
  {
    accessorKey: "existsInReport14",
    header: "En Reporte F14",
    cell: ({ row }) => {
      const exists = row.getValue("existsInReport14");
      return (
        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
          exists 
            ? "bg-green-100 text-green-800" 
            : "bg-red-100 text-red-800"
        }`}>
          {exists ? "Sí" : "No"}
        </div>
      );
    },
  },
  {
    accessorKey: "companyNIT",
    header: "NIT Empresa",
    cell: ({ row }) => (
      <div>{row.getValue("companyNIT")}</div>
    ),
  },
  {
    accessorKey: "nuap",
    header: "NUAP",
    cell: ({ row }) => {
      const nuap = row.getValue("nuap") as number;
      return (
        <div className={nuap === 0 ? "text-gray-400" : ""}>
          {nuap === 0 ? "N/A" : nuap.toString()}
        </div>
      );
    },
  },
  {
    accessorKey: "placeOriginNumber",
    header: "Núm. Lugar Origen",
    cell: ({ row }) => {
      const placeOrigin = row.getValue("placeOriginNumber") as number | null;
      return (
        <div className={placeOrigin === null ? "text-gray-400" : ""}>
          {placeOrigin === null ? "N/A" : placeOrigin.toString()}
        </div>
      );
    },
  },
];