import { Input, MenuItem, SearchIcon } from "@urbetrack/urbix";
import { useMemo, useState } from "react";
import Submenu from "./components/submenu/submenu";

type MenuProps = {
  searchable?: boolean;
  items: MenuItem[];
  inputPlaceholder?: string;
  selectedItems?: string[];
  onSelectedItemsChange?: (items: string[]) => void;
};

export const Menu = ({
  items,
  searchable,
  inputPlaceholder = "Buscar",
  selectedItems: externalSelectedItems,
  onSelectedItemsChange,
}: MenuProps) => {
  const [internalSelectedItems, setInternalSelectedItems] = useState<string[]>([]);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");

  // Use external selectedItems if provided, otherwise use internal state
  const selectedItems = externalSelectedItems ?? internalSelectedItems;

  const filteredItems = useMemo(() => {
    const filter = (items: MenuItem[], query: string): MenuItem[] => {
      return items
        .map((item) => ({
          ...item,
          items: item.items ? filter(item.items, query) : undefined,
        }))
        .filter(
          (item) =>
            item.label.toLowerCase().includes(query.toLowerCase()) ||
            (item.items && item.items.length > 0)
        );
    };

    return filter(items, searchQuery);
  }, [items, searchQuery]);

  return (
    <>
      {searchable && (
        <Input
          placeholder={inputPlaceholder}
          iconRight={<SearchIcon />}
          onChange={(ev: React.ChangeEvent<HTMLInputElement>) => {
            setSearchQuery(ev.target.value);
          }}
        />
      )}
      {filteredItems.map((item) => (
        <Submenu
          key={item.value}
          item={item}
          selectedItems={selectedItems}
          setSelectedItems={(items) => {
            // using external selectedItems, don't update internal state
            if (!externalSelectedItems) {
              setInternalSelectedItems(items);
            }
            onSelectedItemsChange?.(items);
          }}
          setExpandedItems={setExpandedItems}
          expandedItems={expandedItems}
          onSelectedItemsChange={onSelectedItemsChange}
        />
      ))}
    </>
  );
};
